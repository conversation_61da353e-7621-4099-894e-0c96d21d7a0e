import {
  LoginRequest,
  RegisterRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest,
  AuthResponse,
} from "../../../types/auth";
import { apiClient } from "../../../services/apiClient";
import { toast } from "../../../hooks/use-toast";

class AuthService {
  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<AuthResponse> {
    try {
      const method = options.method || "POST";
      const body = options.body;

      let response;
      if (method === "GET") {
        response = await apiClient.get(endpoint, { skipAuth: true });
      } else {
        const data = body ? JSON.parse(body as string) : undefined;
        response = await apiClient.post(endpoint, data, { skipAuth: true });
      }

      return {
        success: response.success,
        data: response.data,
        message: response.message,
      };
    } catch (error) {
      // Enhanced error handling for auth-specific scenarios
      let errorMessage =
        error instanceof Error ? error.message : "An error occurred";

      if (errorMessage.includes("401")) {
        if (
          errorMessage.includes("not found") ||
          errorMessage.includes("does not exist")
        ) {
          errorMessage = "No account found with this email address";
        } else if (
          errorMessage.includes("Invalid credentials") ||
          errorMessage.includes("incorrect")
        ) {
          errorMessage =
            "Invalid email or password. Please check your credentials.";
        } else {
          errorMessage =
            "Authentication failed. Please verify your login details.";
        }
      } else if (errorMessage.includes("409")) {
        errorMessage =
          "This email is already registered. Please use a different email or try logging in.";
      } else if (errorMessage.includes("423")) {
        errorMessage =
          "Account temporarily locked due to multiple failed login attempts. Please try again later.";
      } else if (errorMessage.includes("500")) {
        errorMessage =
          "Internal server error. Our team has been notified. Please try again later.";
      }

      return {
        success: false,
        message: errorMessage,
      };
    }
  }

  async login(credentials: LoginRequest): Promise<AuthResponse> {
    // Convert gmail to email for backend compatibility
    const backendCredentials = {
      email: credentials.gmail,
      password: credentials.password,
    };

    const response = await this.makeRequest("/auth/login", {
      method: "POST",
      body: JSON.stringify(backendCredentials),
    });

    // Don't show toast here - let the calling component handle it
    return response;
  }

  async register(userData: RegisterRequest): Promise<AuthResponse> {
    // Convert gmail to email and align field names for backend
    const backendUserData = {
      name: userData.fullName || userData.gmail?.split("@")[0] || "User",
      email: userData.gmail,
      password: userData.password,
      mobile: userData.mobile || undefined, // Don't send empty string
      address: userData.address || undefined, // Don't send empty string
    };

    // Remove undefined fields
    Object.keys(backendUserData).forEach((key) => {
      if (backendUserData[key] === undefined) {
        delete backendUserData[key];
      }
    });

    const response = await this.makeRequest("/auth/register", {
      method: "POST",
      body: JSON.stringify(backendUserData),
    });

    // Don't show toast here - let the calling component handle it
    return response;
  }

  async forgotPassword(data: ForgotPasswordRequest): Promise<AuthResponse> {
    // Convert gmail to email for backend
    const backendData = {
      email: data.gmail,
    };

    return this.makeRequest("/auth/forgot-password", {
      method: "POST",
      body: JSON.stringify(backendData),
    });
  }

  async resetPassword(data: ResetPasswordRequest): Promise<AuthResponse> {
    return this.makeRequest("/auth/reset-password", {
      method: "POST",
      body: JSON.stringify({
        token: data.token,
        newPassword: data.password,
      }),
    });
  }

  async checkGmailExists(gmail: string): Promise<boolean> {
    try {
      await apiConfig.initialize();
      const baseUrl = apiConfig.getBaseUrl();

      const response = await fetch(
        `${baseUrl}/auth/check-email?email=${encodeURIComponent(gmail)}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        console.warn("Email check failed:", response.status);
        return false;
      }

      const data = await response.json();
      return data.exists || false;
    } catch (error) {
      console.warn("Email check error:", error);
      return false;
    }
  }

  async logout(): Promise<void> {
    try {
      await apiConfig.initialize();
      const baseUrl = apiConfig.getBaseUrl();
      const token = this.getStoredToken();

      if (token) {
        await fetch(`${baseUrl}/auth/logout`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });
      }
    } catch (error) {
      console.warn("Logout request failed, clearing local storage anyway");
    } finally {
      localStorage.removeItem("auth-token");
      localStorage.removeItem("auth-user");
      localStorage.removeItem("auth-refresh-token");

      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      });
    }
  }

  getStoredToken(): string | null {
    return localStorage.getItem("auth-token");
  }

  getStoredRefreshToken(): string | null {
    return localStorage.getItem("auth-refresh-token");
  }

  getStoredUser(): any {
    const user = localStorage.getItem("auth-user");
    return user ? JSON.parse(user) : null;
  }

  storeAuth(token: string, user: any, refreshToken?: string): void {
    localStorage.setItem("auth-token", token);
    localStorage.setItem("auth-user", JSON.stringify(user));
    if (refreshToken) {
      localStorage.setItem("auth-refresh-token", refreshToken);
    }
  }

  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = this.getStoredRefreshToken();
    if (!refreshToken) {
      return {
        success: false,
        message: "No refresh token available",
      };
    }

    return this.makeRequest("/auth/refresh-token", {
      method: "POST",
      body: JSON.stringify({ refreshToken }),
    });
  }
}

export const authService = new AuthService();
