import { create } from 'zustand';
import { userService, UserProfile, UserActivity, UserStats } from '../services/userService';

interface UserStore {
  // State
  profile: UserProfile | null;
  activities: UserActivity[];
  stats: UserStats | null;
  loading: boolean;
  error: string | null;
  
  // Actions
  fetchProfile: () => Promise<void>;
  updateProfile: (data: any) => Promise<void>;
  uploadProfilePicture: (file: File) => Promise<void>;
  fetchActivities: (params?: any) => Promise<void>;
  fetchStats: (days?: number) => Promise<void>;
  updatePreferences: (preferences: any) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  deleteAccount: (password: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

export const useUserStore = create<UserStore>((set, get) => ({
  // Initial state
  profile: null,
  activities: [],
  stats: null,
  loading: false,
  error: null,

  // Actions
  fetchProfile: async () => {
    set({ loading: true, error: null });
    try {
      const profile = await userService.getProfile();
      set({ profile, loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  updateProfile: async (data) => {
    set({ loading: true, error: null });
    try {
      const updatedProfile = await userService.updateProfile(data);
      set({ profile: updatedProfile, loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  uploadProfilePicture: async (file) => {
    set({ loading: true, error: null });
    try {
      const result = await userService.uploadProfilePicture(file);
      const currentProfile = get().profile;
      if (currentProfile) {
        set({ 
          profile: { 
            ...currentProfile, 
            profilePictureUrl: result.profilePictureUrl 
          }, 
          loading: false 
        });
      }
    } catch (error: any) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  fetchActivities: async (params = {}) => {
    set({ loading: true, error: null });
    try {
      const result = await userService.getActivity(params);
      set({ activities: result.activities, loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  fetchStats: async (days = 30) => {
    set({ loading: true, error: null });
    try {
      const stats = await userService.getStats(days);
      set({ stats, loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
    }
  },

  updatePreferences: async (preferences) => {
    set({ loading: true, error: null });
    try {
      await userService.updatePreferences(preferences);
      const currentProfile = get().profile;
      if (currentProfile) {
        set({ 
          profile: { 
            ...currentProfile, 
            ...preferences 
          }, 
          loading: false 
        });
      }
    } catch (error: any) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  changePassword: async (currentPassword, newPassword) => {
    set({ loading: true, error: null });
    try {
      await userService.changePassword(currentPassword, newPassword);
      set({ loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  deleteAccount: async (password) => {
    set({ loading: true, error: null });
    try {
      await userService.deleteAccount(password);
      set({ profile: null, activities: [], stats: null, loading: false });
    } catch (error: any) {
      set({ error: error.message, loading: false });
      throw error;
    }
  },

  setLoading: (loading) => set({ loading }),
  setError: (error) => set({ error }),
  clearError: () => set({ error: null }),
}));