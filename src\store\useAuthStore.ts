import { create } from 'zustand';
import { AuthState, User } from '../types/auth';
import { authService } from '../features/auth/services/authService';

interface AuthStore extends AuthState {
  login: (gmail: string, password: string) => Promise<boolean>;
  register: (gmail: string, password: string, fullName?: string) => Promise<boolean>;
  logout: () => void;
  skipLogin: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  initializeAuth: () => void;
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  isAuthenticated: false,
  user: null,
  token: null,
  loading: false,
  error: null,

  login: async (gmail: string, password: string, rememberMe: boolean = false) => {
    set({ loading: true, error: null });
    
    const response = await authService.login({ gmail, password });
    
    if (response.success && response.user && response.token) {
      // Store auth based on remember me preference
      if (rememberMe) {
        authService.storeAuth(response.token, response.user, response.refreshToken);
      } else {
        // Use session storage for temporary login
        sessionStorage.setItem('auth-token', response.token);
        sessionStorage.setItem('auth-user', JSON.stringify(response.user));
        if (response.refreshToken) {
          sessionStorage.setItem('auth-refresh-token', response.refreshToken);
        }
      }
      
      set({
        isAuthenticated: true,
        user: response.user,
        token: response.token,
        loading: false,
        error: null,
      });
      return true;
    } else {
      set({
        loading: false,
        error: response.message,
      });
      throw new Error(response.message);
    }
  },

  register: async (gmail: string, password: string, fullName?: string, mobile?: string, address?: string) => {
    set({ loading: true, error: null });
    
    const response = await authService.register({ 
      gmail, 
      password, 
      fullName,
      mobile,
      address
    });
    
    if (response.success) {
      set({ loading: false, error: null });
      return true;
    } else {
      set({
        loading: false,
        error: response.message,
      });
      throw new Error(response.message);
    }
  },

  logout: async () => {
    set({ loading: true });
    await authService.logout();
    set({
      isAuthenticated: false,
      user: null,
      token: null,
      loading: false,
      error: null,
    });
  },

  skipLogin: () => {
    set({
      isAuthenticated: true,
      user: { id: 'guest', gmail: '<EMAIL>', fullName: 'Guest User' },
      token: 'guest-token',
      error: null,
    });
  },

  setLoading: (loading: boolean) => set({ loading }),
  setError: (error: string | null) => set({ error }),

  initializeAuth: () => {
    // Check localStorage first (remember me)
    let token = authService.getStoredToken();
    let user = authService.getStoredUser();
    
    // If not found, check sessionStorage (temporary login)
    if (!token) {
      token = sessionStorage.getItem('auth-token');
      const userStr = sessionStorage.getItem('auth-user');
      user = userStr ? JSON.parse(userStr) : null;
    }
    
    if (token && user) {
      set({
        isAuthenticated: true,
        user,
        token,
      });
    }
  },
}));