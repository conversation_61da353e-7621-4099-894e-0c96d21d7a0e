import React, { useState, useEffect, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Progress } from '../../../components/ui/progress';
import { PasswordStrengthMeter } from '../../../components/common/PasswordStrengthMeter';
import { RegistrationSuccessDialog } from './RegistrationSuccessDialog';
import { useAuthForm } from '../hooks/useAuthForm';
import { useTheme } from '../../../providers/ThemeProvider';
import { useGradientTheme } from '../../../providers/GradientThemeProvider';
import { Eye, EyeOff, Mail, Lock, User, CheckCircle, XCircle } from 'lucide-react';
import '../../../styles/tooltip.css';
import '../../../styles/autofill-fix.css';

interface RegisterFormProps {
  onSuccess: () => void;
  onSwitchToLogin: () => void;
}

export const RegisterForm: React.FC<RegisterFormProps> = memo(({ 
  onSuccess, 
  onSwitchToLogin 
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [gmailChecking, setGmailChecking] = useState(false);
  const [gmailAvailable, setGmailAvailable] = useState<boolean | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);

  const { themeConfig } = useTheme();
  const { themeConfig: gradientTheme } = useGradientTheme();
  const handleSuccessWithDialog = useCallback(() => {
    setShowSuccessDialog(true);
    setTimeout(() => {
      onSuccess();
    }, 3000);
  }, [onSuccess]);
  
  const { 
    formData, 
    errors, 
    updateField, 
    handleRegister, 
    validatePassword,
    checkGmailExists 
  } = useAuthForm({ onSuccess: handleSuccessWithDialog });

  const isFormValid = useCallback(() => {
    return formData.gmail && formData.password && formData.confirmPassword && 
           formData.password === formData.confirmPassword && gmailAvailable !== false;
  }, [formData.gmail, formData.password, formData.confirmPassword, gmailAvailable]);

  const togglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const toggleConfirmPassword = useCallback(() => {
    setShowConfirmPassword(prev => !prev);
  }, []);

  useEffect(() => {
    if (formData.password) {
      const validation = validatePassword(formData.password);
      const strength = (5 - validation.errors.length) * 20;
      setPasswordStrength(strength);
    } else {
      setPasswordStrength(0);
    }
  }, [formData.password, validatePassword]);

  useEffect(() => {
    const checkGmail = async () => {
      if (formData.gmail && formData.gmail.includes('@gmail.com')) {
        setGmailChecking(true);
        const exists = await checkGmailExists(formData.gmail);
        setGmailAvailable(!exists);
        setGmailChecking(false);
      } else {
        setGmailAvailable(null);
      }
    };

    const timeoutId = setTimeout(checkGmail, 500);
    return () => clearTimeout(timeoutId);
  }, [formData.gmail, checkGmailExists]);

  const onSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    await handleRegister();
  }, [handleRegister]);



  return (
    <>
      <motion.form
        onSubmit={onSubmit}
        className="space-y-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
      <div className="space-y-4">
        <motion.div
          animate={{
            color: `rgb(${themeConfig.colors.secondary})`,
          }}
          transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
        >
          <Label htmlFor="fullName" className="text-base font-semibold">Full Name (Optional)</Label>
        </motion.div>
        <div className="relative group">
          <User className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            id="fullName"
            type="text"
            placeholder="Your full name"
            value={formData.fullName || ''}
            onChange={(e) => updateField('fullName', e.target.value)}
            className="pl-12 pr-4 py-4 text-base h-12"
            disabled={isSubmitting}
          />
          <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
            Enter your full name (optional)
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <motion.div
          animate={{
            color: `rgb(${themeConfig.colors.primary})`,
          }}
          transition={{ duration: 2, repeat: Infinity, repeatType: "reverse" }}
        >
          <Label htmlFor="gmail" className="text-base font-semibold">Gmail Address</Label>
        </motion.div>
        <div className="relative group">
          <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            id="gmail"
            type="email"
            placeholder="Enter your email"
            value={formData.gmail || ''}
            onChange={(e) => updateField('gmail', e.target.value)}
            className={`pl-12 pr-14 py-4 text-base h-12 transition-all duration-200 ${
              errors.gmail ? 'border-red-500 animate-shake' : ''
            }`}
            disabled={isSubmitting}
          />
          <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
            Enter your Gmail address - we'll check if it's available
          </div>
          {gmailChecking && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2 flex items-center justify-center">
              <motion.div 
                className="h-5 w-5 rounded-full border-2 border-t-transparent"
                style={{ borderColor: `rgb(${themeConfig.colors.primary})`, borderTopColor: 'transparent' }}
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
            </div>
          )}
          {!gmailChecking && gmailAvailable !== null && (
            <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
              {gmailAvailable ? (
                <CheckCircle className="h-5 w-5 text-success" />
              ) : (
                <XCircle className="h-5 w-5 text-destructive" />
              )}
            </div>
          )}
        </div>
        {errors.gmail && (
          <div className="glass-light px-3 py-2 rounded-xl border border-destructive/20">
            <p className="text-sm text-destructive font-medium">{errors.gmail}</p>
          </div>
        )}
        {!gmailChecking && gmailAvailable === false && (
          <div className="glass-light px-3 py-2 rounded-xl border border-destructive/20">
            <p className="text-sm text-destructive font-medium">This Gmail is already registered</p>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <motion.div
          animate={{
            color: `rgb(${themeConfig.colors.accent})`,
          }}
          transition={{ duration: 2.5, repeat: Infinity, repeatType: "reverse" }}
        >
          <Label htmlFor="password" className="text-base font-semibold">Password</Label>
        </motion.div>
        <div className="relative group">
          <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Create a strong password"
            value={formData.password || ''}
            onChange={(e) => updateField('password', e.target.value)}
            className={`pl-12 pr-14 py-4 text-base h-12 transition-all duration-200 ${
              errors.password ? 'border-red-500 animate-shake' : ''
            }`}
            disabled={isSubmitting}
          />
          <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
            Create a strong password with uppercase, lowercase, numbers, and special characters
          </div>
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 rounded-xl transition-all duration-[25ms] hover:bg-primary/20 flex items-center justify-center focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                onClick={togglePassword}
              >
                <motion.div
                  animate={{ rotateY: showPassword ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center justify-center"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
        {formData.password && (
          <PasswordStrengthMeter password={formData.password} />
        )}
        {errors.password && (
          <div className="glass-light px-3 py-2 rounded-xl border border-destructive/20">
            <p className="text-sm text-destructive font-medium">{errors.password}</p>
          </div>
        )}
      </div>

      <div className="space-y-4">
        <motion.div
          animate={{
            color: `rgb(${themeConfig.colors.primary})`,
          }}
          transition={{ duration: 3, repeat: Infinity, repeatType: "reverse" }}
        >
          <Label htmlFor="confirmPassword" className="text-base font-semibold">Confirm Password</Label>
        </motion.div>
        <div className="relative group">
          <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            id="confirmPassword"
            type={showConfirmPassword ? 'text' : 'password'}
            placeholder="Confirm your password"
            value={formData.confirmPassword || ''}
            onChange={(e) => updateField('confirmPassword', e.target.value)}
            className={`pl-12 pr-14 py-4 text-base h-12 transition-all duration-200 ${
              (errors.confirmPassword || (formData.confirmPassword && formData.password && formData.confirmPassword !== formData.password)) ? 'border-red-500 animate-shake' : ''
            }`}
            disabled={isSubmitting}
          />
          <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
            Re-enter your password to confirm it matches
          </div>
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0 rounded-xl transition-all duration-[25ms] hover:bg-primary/20 flex items-center justify-center focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                onClick={toggleConfirmPassword}
              >
                <motion.div
                  animate={{ rotateY: showConfirmPassword ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center justify-center"
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
        {formData.confirmPassword && formData.password && formData.confirmPassword !== formData.password && (
          <motion.div 
            className="glass-light px-3 py-2 rounded-xl border border-red-500/20 bg-red-500/5"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <p className="text-sm text-red-500 font-medium flex items-center gap-2">
              <XCircle className="h-3 w-3" />
              Passwords don't match
            </p>
          </motion.div>
        )}
        {errors.confirmPassword && (
          <p className="text-sm text-destructive ml-1">{errors.confirmPassword}</p>
        )}
      </div>

      <div className="pt-2">
        <Button
          type="submit"
          className="w-full h-11 text-base font-semibold transition-all duration-[25ms]"
          disabled={isSubmitting || !isFormValid()}
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
              <span>Creating Account...</span>
            </div>
          ) : (
            'Create Account'
          )}
        </Button>
      </div>

      <div className="text-center pt-2">
        <p className="text-base text-muted-foreground">
          Already have an account?{' '}
          <Button
            type="button"
            variant="link"
            className="p-0 h-auto text-base font-semibold"
            onClick={onSwitchToLogin}
          >
            Sign in
          </Button>
        </p>
      </div>
      </motion.form>
      
      <RegistrationSuccessDialog 
        isOpen={showSuccessDialog} 
        onClose={() => setShowSuccessDialog(false)}
        theme={gradientTheme}
      />
    </>
  );
});