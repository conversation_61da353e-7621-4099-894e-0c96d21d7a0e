// Global Search Routes
import express from 'express';
import <PERSON><PERSON> from 'joi';
import { validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken } from '../../middleware/auth.middleware.js';
import { apiRateLimit } from '../../middleware/rateLimit.js';
import {
  globalSearch,
  getSearchSuggestions
} from '../../controllers/search.controller.js';

const router = express.Router();

// Apply authentication and rate limiting to all routes
router.use(authenticateToken);
router.use(apiRateLimit);

// Validation schemas
const searchQuerySchema = Joi.object({
  q: Joi.string().min(2).max(100).required(),
  type: Joi.string().valid('all', 'grievance', 'user', 'activity').default('all'),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  dateRange: Joi.string().valid('all', '7d', '30d', '90d', 'custom').default('all'),
  status: Joi.string().valid('all', 'submitted', 'in_review', 'in_progress', 'assigned', 'resolved', 'rejected').optional(),
  category: Joi.string().valid('all', 'Infrastructure', 'Sanitation', 'Utilities', 'Transportation', 'Public Safety', 'Healthcare', 'Education', 'Environment', 'Other').optional(),
  priority: Joi.string().valid('all', 'low', 'medium', 'high', 'urgent', 'Normal').optional(),
  dateFrom: Joi.date().iso().optional(),
  dateTo: Joi.date().iso().min(Joi.ref('dateFrom')).optional()
});

const suggestionsQuerySchema = Joi.object({
  q: Joi.string().min(1).max(50).required()
});

// Routes

// Global search
router.get('/',
  validateQuery(searchQuerySchema),
  globalSearch
);

// Search suggestions
router.get('/suggestions',
  validateQuery(suggestionsQuerySchema),
  getSearchSuggestions
);

export default router;