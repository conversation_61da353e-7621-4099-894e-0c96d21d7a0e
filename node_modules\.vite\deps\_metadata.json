{"hash": "1d695357", "configHash": "cf8c31cd", "lockfileHash": "dbe70987", "browserHash": "aadbf897", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "3a13beb5", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "1fc1d293", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "e7a68f70", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "ce79a4c9", "needsInterop": true}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "e9012f13", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "8fa61371", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "7331fd8a", "needsInterop": false}, "@radix-ui/react-icons": {"src": "../../@radix-ui/react-icons/dist/react-icons.esm.js", "file": "@radix-ui_react-icons.js", "fileHash": "83f51f9b", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "cbf8a1e4", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "5c5cc57f", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "70a8fc1d", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "e5f8b710", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "a141abf8", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "27cbef7b", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "6581bc5e", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "e53b810c", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "559f71e9", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "8dc4372d", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "7cdeb7b7", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "974ae08b", "needsInterop": false}}, "chunks": {"chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-NJ2ENSFT": {"file": "chunk-NJ2ENSFT.js"}, "chunk-4EFJBM2F": {"file": "chunk-4EFJBM2F.js"}, "chunk-MLSUYXFR": {"file": "chunk-MLSUYXFR.js"}, "chunk-36WGJFHK": {"file": "chunk-36WGJFHK.js"}, "chunk-NAUERH6Y": {"file": "chunk-NAUERH6Y.js"}, "chunk-ZZM5ZGHR": {"file": "chunk-ZZM5ZGHR.js"}, "chunk-YQ5BCTVV": {"file": "chunk-YQ5BCTVV.js"}, "chunk-NRBATONI": {"file": "chunk-NRBATONI.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}