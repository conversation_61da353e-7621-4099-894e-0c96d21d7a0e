{"category":"security","email":"tes***","environment":"development","event":"login_attempt","ip":"127.0.0.1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-07 19:13:55.646","userAgent":"test-agent","version":"1.0.0"}
{"action":"test_action","category":"security","environment":"development","event":"admin_access","ip":"127.0.0.1","level":"info","message":"Admin access","service":"civicassist-backend","timestamp":"2025-07-07 19:13:55.648","userId":"test-admin","version":"1.0.0"}
{"activity":"file_quarantined","category":"security","environment":"development","event":"suspicious_activity","fileSize":4,"filename":"test.exe","ip":null,"level":"warn","message":"Suspicious activity","quarantinePath":"C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\quarantine\\2025-07-08T02-13-55-665Z_test.exe","reason":"Executable file detected","service":"civicassist-backend","timestamp":"2025-07-07 19:13:55.667","userId":null,"version":"1.0.0"}
{"activity":"malicious_file_detected","category":"security","environment":"development","event":"suspicious_activity","fileHash":"20879c987e2f9a916e578386d499f629","filename":"test.exe","ip":null,"level":"warn","message":"Suspicious activity","service":"civicassist-backend","threats":["Executable file detected"],"timestamp":"2025-07-07 19:13:55.669","userId":null,"version":"1.0.0"}
{"category":"security","email":"tes***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 05:03:22.964","userAgent":"node-fetch","version":"1.0.0"}
{"category":"security","email":"tes***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 05:09:18.607","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)","version":"1.0.0"}
{"category":"security","email":"tes***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 05:14:48.110","userAgent":"node-fetch/1.0 (+https://github.com/bitinn/node-fetch)","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 06:23:31.517","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 07:51:25.679","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 11:26:49.853","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-09 11:26:57.407","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-10 01:17:36.403","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-10 03:21:55.287","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-10 03:22:22.254","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"category":"security","email":"pra***","environment":"development","event":"login_attempt","ip":"::1","level":"info","message":"Login attempt","service":"civicassist-backend","success":true,"timestamp":"2025-07-10 05:04:30.404","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
