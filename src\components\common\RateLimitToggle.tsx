import React, { useState } from "react";
import { motion } from "framer-motion";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import { Zap } from "lucide-react";
import { rateLimitUtils } from "../../utils/rateLimitUtils";

interface RateLimitToggleProps {
  onToggle: (disabled: boolean) => void;
}

export const RateLimitToggle: React.FC<RateLimitToggleProps> = ({
  onToggle,
}) => {
  const [isDisabled, setIsDisabled] = useState(rateLimitUtils.isDisabled());

  const handleToggle = (checked: boolean) => {
    setIsDisabled(checked);
    onToggle(checked);

    // Use the utility to manage state
    if (checked) {
      rateLimitUtils.disable();
    } else {
      rateLimitUtils.enable();
    }
  };

  // Initialize from localStorage
  React.useEffect(() => {
    const currentState = rateLimitUtils.isDisabled();
    setIsDisabled(currentState);
    onToggle(currentState);
    rateLimitUtils.logStatus();
  }, [onToggle]);

  return (
    <motion.div
      className="fixed bottom-4 left-4 z-50 p-3 rounded-lg glass border"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 1 }}
    >
      <div className="flex items-center space-x-2">
        <Zap className="h-4 w-4 text-yellow-500" />
        <Label htmlFor="rate-limit-toggle" className="text-sm font-medium">
          Bypass Rate Limit
        </Label>
        <Switch
          id="rate-limit-toggle"
          checked={isDisabled}
          onCheckedChange={handleToggle}
        />
      </div>
      {isDisabled && (
        <motion.p
          className="text-xs text-yellow-400 mt-1"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
        >
          Rate limiting disabled
        </motion.p>
      )}
    </motion.div>
  );
};
