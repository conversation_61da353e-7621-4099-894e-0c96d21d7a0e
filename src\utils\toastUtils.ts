import { toast } from '../hooks/use-toast';

export interface ToastOptions {
  title: string;
  description?: string;
  duration?: number;
  variant?: 'default' | 'destructive' | 'success';
}

export const toastUtils = {
  // Success toasts
  success: (options: Omit<ToastOptions, 'variant'>) => {
    return toast({
      ...options,
      variant: 'default',
      title: `✅ ${options.title}`,
    });
  },

  // Error toasts with specific error handling
  error: (options: Omit<ToastOptions, 'variant'>) => {
    return toast({
      ...options,
      variant: 'destructive',
      title: `❌ ${options.title}`,
    });
  },

  // Warning toasts
  warning: (options: Omit<ToastOptions, 'variant'>) => {
    return toast({
      ...options,
      variant: 'default',
      title: `⚠️ ${options.title}`,
    });
  },

  // Info toasts
  info: (options: Omit<ToastOptions, 'variant'>) => {
    return toast({
      ...options,
      variant: 'default',
      title: `ℹ️ ${options.title}`,
    });
  },

  // Authentication specific toasts
  auth: {
    loginSuccess: () => {
      return toast({
        title: "🎉 Login Successful!",
        description: "Welcome back! Redirecting to dashboard...",
        duration: 2000,
      });
    },

    loginFailed: (reason?: string) => {
      let title = "❌ Login Failed";
      let description = "Please check your credentials and try again.";

      if (reason?.includes('not found') || reason?.includes('No account')) {
        title = "🔍 Account Not Found";
        description = "No account found with this email address.";
      } else if (reason?.includes('Invalid') || reason?.includes('wrong')) {
        title = "🔑 Invalid Credentials";
        description = "Invalid email or password. Please check your credentials.";
      } else if (reason?.includes('locked')) {
        title = "🔒 Account Locked";
        description = "Account temporarily locked due to multiple failed attempts.";
      } else if (reason?.includes('Network') || reason?.includes('timeout')) {
        title = "🌐 Connection Error";
        description = "Network timeout. Please check your connection.";
      } else if (reason?.includes('server error')) {
        title = "⚠️ Server Error";
        description = "Internal server error. Please try again later.";
      }

      return toast({
        title,
        description,
        variant: 'destructive',
      });
    },

    registrationSuccess: () => {
      return toast({
        title: "🎉 Registration Successful!",
        description: "Please log in with your new account.",
        duration: 3000,
      });
    },

    registrationFailed: (reason?: string) => {
      let title = "❌ Registration Failed";
      let description = "Please check your information and try again.";

      if (reason?.includes('already registered') || reason?.includes('already exists')) {
        title = "📧 Email Already Exists";
        description = "This email is already registered. Please use a different email.";
      } else if (reason?.includes('Network') || reason?.includes('timeout')) {
        title = "🌐 Connection Error";
        description = "Network timeout. Please check your connection.";
      } else if (reason?.includes('server error')) {
        title = "⚠️ Server Error";
        description = "Internal server error. Please try again later.";
      }

      return toast({
        title,
        description,
        variant: 'destructive',
      });
    },

    passwordResetSent: () => {
      return toast({
        title: "📧 Reset Link Sent",
        description: "If an account exists, a reset link has been sent to your email.",
        duration: 4000,
      });
    },

    passwordResetFailed: (reason?: string) => {
      let title = "❌ Password Reset Failed";
      let description = "Unable to process password reset request.";

      if (reason?.includes('not found') || reason?.includes('No account')) {
        title = "🔍 No Account Found";
        description = "No account found with this email address.";
      } else if (reason?.includes('expired') || reason?.includes('invalid')) {
        title = "⏰ Link Expired";
        description = "Password reset link has expired. Please request a new one.";
      }

      return toast({
        title,
        description,
        variant: 'destructive',
      });
    },

    emailValidation: {
      invalid: () => {
        return toast({
          title: "📧 Invalid Email Format",
          description: "Please enter a valid email address.",
          variant: 'destructive',
          duration: 3000,
        });
      },

      alreadyExists: () => {
        return toast({
          title: "📧 Email Already Registered",
          description: "This email is already in use. Please use a different email.",
          variant: 'destructive',
          duration: 3000,
        });
      },

      available: () => {
        return toast({
          title: "✅ Email Available",
          description: "This email is available for registration.",
          duration: 2000,
        });
      },
    },

    validation: {
      emptyFields: () => {
        return toast({
          title: "📝 Required Fields Missing",
          description: "Please fill in all required fields.",
          variant: 'destructive',
          duration: 3000,
        });
      },

      passwordMismatch: () => {
        return toast({
          title: "🔑 Passwords Don't Match",
          description: "Password and confirm password must match.",
          variant: 'destructive',
          duration: 3000,
        });
      },

      weakPassword: () => {
        return toast({
          title: "🔒 Password Too Weak",
          description: "Password must meet security requirements.",
          variant: 'destructive',
          duration: 3000,
        });
      },
    },

    rateLimiting: {
      tooManyAttempts: () => {
        return toast({
          title: "⏱️ Too Many Attempts",
          description: "Please wait a moment before trying again.",
          variant: 'destructive',
          duration: 3000,
        });
      },

      pleaseWait: () => {
        return toast({
          title: "⏳ Please Wait",
          description: "Please wait a moment before trying again.",
          variant: 'destructive',
          duration: 2000,
        });
      },
    },
  },
};
