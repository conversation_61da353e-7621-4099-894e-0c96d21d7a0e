@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for Dynamic Theming */
:root {
  /* Default theme (Midnight Eclipse) */
  --color-primary: 15 23 42;
  --color-secondary: 30 41 59;
  --color-accent: 99 102 241;
  --color-background: 2 6 23;
  --color-foreground: 248 250 252;
  --color-card: 15 23 42;
  --color-card-foreground: 226 232 240;
  --color-popover: 15 23 42;
  --color-popover-foreground: 248 250 252;
  --color-muted: 30 41 59;
  --color-muted-foreground: 148 163 184;
  --color-border: 30 41 59;
  --color-input: 30 41 59;
  --color-ring: 99 102 241;
  --color-destructive: 239 68 68;
  --color-destructive-foreground: 248 250 252;
  --color-success: 34 197 94;
  --color-warning: 251 191 36;
  --color-logo-glow: 99 102 241;
  --color-logo-beam: 139 92 246;
  --color-logo-particle: 59 130 246;

  /* Roundness hierarchy - Galaxy Spiral Standard */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 24px;
  --radius-2xl: 32px;
  --radius-3xl: 40px;
  --radius-full: 9999px;

  /* Galaxy Spiral Universal Standards - Non-Color Attributes */

  /* Component Dimensions */
  --button-height-sm: 2rem;
  --button-height-md: 2.25rem;
  --button-height-lg: 2.5rem;
  --button-width-icon: 2.25rem;
  --input-height: 3rem;
  --logo-size-sm: 2rem;
  --logo-size-md: 3rem;
  --logo-size-lg: 4rem;
  --logo-size-xl: 6rem;

  /* Spacing Scale - Galaxy Spiral Standard */
  --space-0: 0;
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-12: 3rem;
  --space-14: 3.5rem;
  --space-16: 4rem;
  --space-18: 4.5rem;
  --space-20: 5rem;
  --space-88: 22rem;

  /* Animation Durations - Galaxy Spiral Standard */
  --duration-instant: 25ms;
  --duration-fast: 75ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 2s;
  --duration-slowest: 6s;
  --duration-ultra-slow: 20s;
  --duration-mega-slow: 30s;

  /* Component Specific - Galaxy Spiral Standard */
  --theme-switcher-size: 3rem;
  --theme-switcher-modal-size: 8rem;
  --theme-switcher-radius: 45px;
  --theme-switcher-offset: 12px;
  --theme-switcher-icon-sm: 0.75rem;
  --theme-switcher-icon-md: 1.5rem;

  /* Logo Container - Galaxy Spiral Standard */
  --logo-container-size: 120px;
  --logo-glow-size: 150px;
  --logo-ring-1: 180px;
  --logo-ring-2: 150px;
  --logo-ring-3: 120px;
  --logo-outer-size: 90px;
  --logo-inner-size: 60px;
  --logo-core-size: 40px;

  /* Transform & Scale Values - Galaxy Spiral Standard */
  --scale-hover: 1.05;
  --scale-active: 0.95;
  --scale-theme-current: 1.25;
  --scale-theme-hover-current: 1.8;
  --scale-theme-hover: 1.6;
  --slide-y: 10px;
  --shimmer-start: -200%;
  --shimmer-end: 200%;

  /* Icon Sizes - Galaxy Spiral Standard */
  --icon-xs: 0.75rem;
  --icon-sm: 1rem;
  --icon-md: 1.25rem;
  --icon-lg: 1.5rem;
  --icon-xl: 2rem;

  /* Glassmorphism Variables - Dynamic */
  --glass-blur: 10px;
  --glass-bg-opacity: 0.05;
  --glass-border-opacity: 0.1;
  --glass-light-bg-opacity: 0.1;
  --glass-light-border-opacity: 0.15;
  --glass-heavy-bg-opacity: 0.02;
  --glass-heavy-border-opacity: 0.08;
}

/* Base styles */
* {
  border-color: rgb(var(--color-border));
}

body {
  background-color: rgb(var(--color-background));
  color: rgb(var(--color-foreground));
  font-feature-settings: "rlig" 1, "calt" 1;
}

/* Smooth transitions for theme changes */
*,
*::before,
*::after {
  transition-property: color, background-color, border-color,
    text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter,
    backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

/* Focus styles using theme colors */
*:focus-visible {
  outline: 2px solid rgb(var(--color-ring));
  outline-offset: 2px;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar with theme colors */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgb(var(--color-muted));
  border-radius: var(--radius-sm);
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(var(--color-muted-foreground));
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgb(var(--color-muted)) transparent;
}

/* Authentication form animations */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Theme-specific animations */
@keyframes theme-pulse {
  0%,
  100% {
    opacity: 0.7;
    box-shadow: 0 0 0 0 rgb(var(--color-primary) / 0.4);
  }
  50% {
    opacity: 1;
    box-shadow: 0 0 0 10px rgb(var(--color-primary) / 0);
  }
}

@keyframes theme-glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgb(var(--color-logo-glow) / 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgb(var(--color-logo-glow) / 0.8),
      0 0 30px rgb(var(--color-logo-beam) / 0.4);
  }
}

@keyframes theme-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Dynamic Glass Effect Utilities */
.glass {
  background: rgba(255, 255, 255, var(--glass-bg-opacity));
  backdrop-filter: blur(var(--glass-blur));
  -webkit-backdrop-filter: blur(var(--glass-blur));
  border: 1px solid rgba(255, 255, 255, var(--glass-border-opacity));
}

.glass-light {
  background: rgba(255, 255, 255, var(--glass-light-bg-opacity));
  backdrop-filter: blur(calc(var(--glass-blur) * 0.8));
  -webkit-backdrop-filter: blur(calc(var(--glass-blur) * 0.8));
  border: 1px solid rgba(255, 255, 255, var(--glass-light-border-opacity));
}

.glass-heavy {
  background: rgba(255, 255, 255, var(--glass-heavy-bg-opacity));
  backdrop-filter: blur(calc(var(--glass-blur) * 1.5));
  -webkit-backdrop-filter: blur(calc(var(--glass-blur) * 1.5));
  border: 1px solid rgba(255, 255, 255, var(--glass-heavy-border-opacity));
}

.glass-overlay {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Utility classes */
.theme-glow {
  animation: theme-glow 2s ease-in-out infinite;
}

.theme-pulse {
  animation: theme-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.theme-shimmer {
  background: linear-gradient(
    90deg,
    transparent,
    rgb(var(--color-primary) / 0.1),
    transparent
  );
  background-size: 200% 100%;
  animation: theme-shimmer 1.5s infinite;
}

/* Theme-specific overrides for better visual hierarchy */
.theme-midnight {
  --shadow-color: 15 23 42;
}

.theme-aurora {
  --shadow-color: 6 78 59;
}

.theme-crimson {
  --shadow-color: 87 13 13;
}

.theme-ocean {
  --shadow-color: 12 74 110;
}

.theme-forest {
  --shadow-color: 22 101 52;
}

.theme-sunset {
  --shadow-color: 154 52 18;
}

.theme-cosmic {
  --shadow-color: 30 41 59;
}

.theme-neon {
  --shadow-color: 30 41 59;
}

.theme-royal {
  --shadow-color: 39 39 42;
}

.theme-ember {
  --shadow-color: 68 64 60;
}

.theme-arctic {
  --shadow-color: 226 232 240;
}

.theme-desert {
  --shadow-color: 168 162 158;
}

.theme-storm {
  --shadow-color: 30 41 59;
}

.theme-galaxy {
  --shadow-color: 15 23 42;
}

.theme-volcano {
  --shadow-color: 127 29 29;
}

.theme-crystal {
  --shadow-color: 248 250 252;
}

/* Shadcn default variables removed - using custom theme system only */

@layer base {
  * {
    border-color: rgb(var(--color-border));
  }
  body {
    background-color: rgb(var(--color-background));
    color: rgb(var(--color-foreground));
  }
}
