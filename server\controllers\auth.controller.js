// Authentication Controller
import jwt from 'jsonwebtoken';
import User from '../models/User.js';
import UserActivity from '../models/UserActivity.js';
import AuthService from '../services/AuthService.js';
import SecurityEventService from '../services/SecurityEventService.js';
import config from '../config/environment.js';
import { AppError, asyncHandler } from '../middleware/error.middleware.js';
import { securityLogger } from '../middleware/logging.middleware.js';

// Generate JWT tokens
const generateTokens = (user) => {
  const payload = {
    userId: user._id,
    email: user.gmail,
    role: user.role
  };

  const accessToken = jwt.sign(payload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRES_IN
  });

  const refreshToken = jwt.sign(payload, config.JWT_REFRESH_SECRET, {
    expiresIn: config.JWT_REFRESH_EXPIRES_IN
  });

  return { accessToken, refreshToken };
};

// Login with enhanced security
const login = asyncHandler(async (req, res) => {
  const { email, password } = req.body;
  const { ip, headers } = req;
  const userAgent = headers['user-agent'];

  // Use enhanced AuthService for login
  const loginResult = await AuthService.handleLoginAttempt(email, password, ip, userAgent);

  if (!loginResult.success) {
    const statusCode = loginResult.locked ? 423 : 401;
    let errorCode = 'INVALID_CREDENTIALS';
    let errorMessage = loginResult.error;

    // Provide specific error messages based on the failure reason
    if (loginResult.userNotFound) {
      errorCode = 'USER_NOT_FOUND';
      errorMessage = 'No account found with this email address';
    } else if (loginResult.locked) {
      errorCode = 'ACCOUNT_LOCKED';
      errorMessage = 'Account temporarily locked due to multiple failed login attempts. Please try again later.';
    } else if (loginResult.wrongPassword) {
      errorCode = 'WRONG_PASSWORD';
      errorMessage = 'Invalid email or password. Please check your credentials.';
    }

    throw new AppError(errorMessage, statusCode, errorCode);
  }

  const { user } = loginResult;

  // Log activity
  await UserActivity.logLogin(user._id, ip, userAgent, true);
  securityLogger.loginAttempt(email, true, ip, userAgent);

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user);

  // Add session to user
  const sessionData = {
    sessionId: jwt.decode(accessToken).jti || Date.now().toString(),
    createdAt: new Date(),
    ipAddress: ip,
    userAgent
  };
  await User.addActiveSession(user._id, sessionData);

  // Prepare user response (exclude sensitive data)
  const userResponse = {
    _id: user._id,
    gmail: user.gmail,
    fullName: user.fullName || '',
    role: 'user',
    profilePictureUrl: user.profilePictureUrl || '',
    isAdmin: false,
    languagePreference: user.languagePreference || 'English',
    notificationPreferences: user.notificationPreferences || {
      statusChange: true,
      newGrievanceFiled: true,
      remarksAdded: true
    }
  };

  res.json({
    success: true,
    message: 'Login successful',
    token: accessToken,
    refreshToken,
    user: userResponse
  });
});

// Register
const register = asyncHandler(async (req, res) => {
  const { name, email, password, mobile, address } = req.body;

  // Check if user already exists
  const existingUser = await User.findByEmail(email);
  if (existingUser) {
    throw new AppError('Email already registered', 409, 'EMAIL_EXISTS');
  }

  // Create new user
  const userData = {
    gmail: email,
    password, // Will be hashed in User.create
    fullName: name,
    phoneNumber: mobile,
    address
  };

  const user = await User.create(userData);

  // Log activity
  await UserActivity.logLogin(user._id, req.ip, req.headers['user-agent'], true);

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    user: {
      _id: user._id,
      gmail: user.gmail,
      fullName: user.fullName
    }
  });
});

// Forgot Password - Send reset email
const forgotPassword = asyncHandler(async (req, res) => {
  const { email } = req.body;
  const { ip } = req;

  const result = await AuthService.sendPasswordResetEmail(email, ip);
  
  res.json({
    success: true,
    message: result.message
  });
});

// Verify Reset Token
const verifyResetToken = asyncHandler(async (req, res) => {
  const { token } = req.query;

  if (!token) {
    throw new AppError('Reset token required', 400, 'TOKEN_REQUIRED');
  }

  const validation = await AuthService.validateResetToken(token);
  
  if (!validation.valid) {
    throw new AppError('Invalid or expired token', 400, 'INVALID_TOKEN');
  }

  res.json({
    success: true,
    message: 'Token is valid'
  });
});

// Reset Password with Token
const resetPassword = asyncHandler(async (req, res) => {
  const { token, newPassword } = req.body;
  const { ip } = req;

  if (!token || !newPassword) {
    throw new AppError('Token and new password required', 400, 'MISSING_FIELDS');
  }

  const result = await AuthService.resetUserPassword(token, newPassword, ip);
  
  if (!result.success) {
    throw new AppError(result.error, 400, 'RESET_FAILED');
  }

  res.json({
    success: true,
    message: 'Password reset successfully'
  });
});

// Change Password (authenticated)
const changePassword = asyncHandler(async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.userId;

  // Get user
  const user = await User.findById(userId);
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND');
  }

  // Verify current password
  const isValidPassword = await User.verifyPassword(currentPassword, user.passwordHash);
  if (!isValidPassword) {
    throw new AppError('Current password is incorrect', 400, 'INVALID_CURRENT_PASSWORD');
  }

  // Update password
  const updated = await User.updatePassword(userId, newPassword);
  if (!updated) {
    throw new AppError('Failed to update password', 500, 'PASSWORD_UPDATE_FAILED');
  }

  // Log activity
  await UserActivity.logPasswordChanged(userId, req.ip);

  // Clear all other sessions for security
  await User.clearAllSessions(userId);

  res.json({
    success: true,
    message: 'Password changed successfully'
  });
});

// Switch to Admin
const switchToAdmin = asyncHandler(async (req, res) => {
  const { password } = req.body;
  const userId = req.user.userId;

  // Get user
  const user = await User.findById(userId);
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND');
  }

  // Verify password
  const isValidPassword = await User.verifyPassword(password, user.passwordHash);
  if (!isValidPassword) {
    throw new AppError('Invalid password', 400, 'INVALID_PASSWORD');
  }

  // Log admin access
  await UserActivity.logAdminAccess(userId, 'admin_switch', req.ip);
  securityLogger.adminAccess(userId, 'admin_switch', req.ip);

  // Generate new token with admin privileges
  const adminPayload = {
    userId: user._id,
    email: user.gmail,
    role: 'admin',
    isAdmin: true
  };

  const adminToken = jwt.sign(adminPayload, config.JWT_SECRET, {
    expiresIn: config.JWT_EXPIRES_IN
  });

  // Update user response
  const userResponse = {
    ...user,
    role: 'admin',
    isAdmin: true
  };

  res.json({
    success: true,
    message: 'Switched to admin mode',
    adminToken,
    user: userResponse
  });
});

// Refresh Token
const refreshToken = asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new AppError('Refresh token required', 400, 'REFRESH_TOKEN_REQUIRED');
  }

  // Verify refresh token
  const decoded = jwt.verify(refreshToken, config.JWT_REFRESH_SECRET);
  
  // Get user
  const user = await User.findById(decoded.userId);
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND');
  }

  // Generate new tokens
  const { accessToken, refreshToken: newRefreshToken } = generateTokens(user);

  res.json({
    success: true,
    token: accessToken,
    refreshToken: newRefreshToken
  });
});

// Logout
const logout = asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const sessionId = req.headers['session-id'];

  // Remove specific session if provided
  if (sessionId) {
    await User.removeActiveSession(userId, sessionId);
  }

  // Log activity
  await UserActivity.logLogout(userId, sessionId);

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Logout from all devices
const logoutAll = asyncHandler(async (req, res) => {
  const userId = req.user.userId;

  // Clear all sessions
  await User.clearAllSessions(userId);

  // Log activity
  await UserActivity.logLogout(userId, 'all_sessions');

  res.json({
    success: true,
    message: 'Logged out from all devices'
  });
});

// Get active sessions
const getActiveSessions = asyncHandler(async (req, res) => {
  const userId = req.user.userId;

  const user = await User.findById(userId);
  if (!user) {
    throw new AppError('User not found', 404, 'USER_NOT_FOUND');
  }

  res.json({
    success: true,
    sessions: user.activeSessions || []
  });
});

// Get security events
const getSecurityEvents = asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const { page = 1, limit = 20, type } = req.query;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit)
  };

  if (type) options.type = type;

  const result = await SecurityEventService.getUserSecurityEvents(userId, options);

  res.json({
    success: true,
    ...result
  });
});

// Check if email exists (for registration validation)
const checkEmailExists = asyncHandler(async (req, res) => {
  const { email } = req.query;

  if (!email) {
    throw new AppError('Email parameter required', 400, 'EMAIL_REQUIRED');
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new AppError('Invalid email format', 400, 'INVALID_EMAIL_FORMAT');
  }

  const existingUser = await User.findByEmail(email);

  res.json({
    success: true,
    exists: !!existingUser,
    message: existingUser ? 'Email is already registered' : 'Email is available'
  });
});

export default {
  login,
  register,
  forgotPassword,
  verifyResetToken,
  resetPassword,
  changePassword,
  switchToAdmin,
  refreshToken,
  logout,
  logoutAll,
  getActiveSessions,
  getSecurityEvents,
  checkEmailExists
};