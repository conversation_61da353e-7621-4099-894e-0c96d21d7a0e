import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../../components/ui/card';
import { X37Logo } from '../../../components/ui/X37Logo';
import { ThemeSwitcher } from '../../../components/ui/ThemeSwitcher';
import { SkipLoginToggle } from '../../../components/common/SkipLoginToggle';
import { AuthBackground } from '../components/AuthBackground';
import { LoginForm } from '../components/LoginForm';
import { RegisterForm } from '../components/RegisterForm';
import { ForgotPasswordForm } from '../components/ForgotPasswordForm';
import { ResetPasswordForm } from '../components/ResetPasswordForm';
import { useAuthStore } from '../../../store/useAuthStore';
import { useTheme } from '../../../providers/ThemeProvider';
import { GradientThemeProvider } from '../../../providers/GradientThemeProvider';
import { RateLimitToggle } from '../../../components/common/RateLimitToggle';

type AuthMode = 'login' | 'register' | 'forgot-password' | 'reset-password';

interface AuthPageProps {
  onAuthSuccess: () => void;
  resetToken?: string;
}

export const AuthPage: React.FC<AuthPageProps> = ({ onAuthSuccess, resetToken }) => {
  const [mode, setMode] = useState<AuthMode>(resetToken ? 'reset-password' : 'login');
  const { error, setError } = useAuthStore();
  const { themeConfig } = useTheme();

  useEffect(() => {
    if (resetToken) {
      setMode('reset-password');
    }
  }, [resetToken]);

  const handleSuccess = () => {
    setError(null);
    onAuthSuccess();
  };

  const getTitle = () => {
    switch (mode) {
      case 'login': return 'Welcome Back';
      case 'register': return 'Create Account';
      case 'forgot-password': return 'Reset Password';
      case 'reset-password': return 'New Password';
      default: return 'Welcome';
    }
  };

  const getDescription = () => {
    switch (mode) {
      case 'login': return 'Sign in to your CivicAssist account';
      case 'register': return 'Join CivicAssist to manage your civic engagement';
      case 'forgot-password': return 'We\'ll help you reset your password';
      case 'reset-password': return 'Enter your new password';
      default: return '';
    }
  };

  return (
    <GradientThemeProvider>
      <div className="min-h-screen flex items-center justify-center p-4 relative">
        <AuthBackground />
      
      <div className="absolute top-4 right-4 z-10 flex items-center gap-4">
        <SkipLoginToggle onSkip={onAuthSuccess} />
        <ThemeSwitcher />
      </div>
      
      <RateLimitToggle onToggle={(disabled) => {
        console.log('Rate limiting', disabled ? 'disabled' : 'enabled');
      }} />

      <motion.div
        className="w-full max-w-md relative z-10"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        <Card className="backdrop-blur-sm bg-card/95 border shadow-xl">
          <CardHeader className="text-center space-y-4">
            <div className="flex justify-center">
              <X37Logo size="sm" animated className="auth-logo" />
            </div>
            <div className="space-y-2">
              <motion.div
                animate={themeConfig ? {
                  color: [
                    `rgb(${themeConfig.colors.primary})`,
                    `rgb(${themeConfig.colors.accent})`,
                    `rgb(${themeConfig.colors.secondary})`,
                    `rgb(${themeConfig.colors.primary})`
                  ]
                } : {}}
                transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
              >
                <CardTitle className="text-2xl font-bold">{getTitle()}</CardTitle>
              </motion.div>
              <CardDescription>{getDescription()}</CardDescription>
            </div>
          </CardHeader>
          
          <CardContent>
            <AnimatePresence mode="wait">
              {mode === 'login' && (
                <motion.div
                  key="login"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <LoginForm
                    onSuccess={handleSuccess}
                    onSwitchToRegister={() => setMode('register')}
                    onForgotPassword={() => setMode('forgot-password')}
                  />
                </motion.div>
              )}
              
              {mode === 'register' && (
                <motion.div
                  key="register"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <RegisterForm
                    onSuccess={() => setMode('login')}
                    onSwitchToLogin={() => setMode('login')}
                  />
                </motion.div>
              )}
              
              {mode === 'forgot-password' && (
                <motion.div
                  key="forgot-password"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <ForgotPasswordForm
                    onBack={() => setMode('login')}
                  />
                </motion.div>
              )}
              
              {mode === 'reset-password' && resetToken && (
                <motion.div
                  key="reset-password"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.2 }}
                >
                  <ResetPasswordForm
                    token={resetToken}
                    onSuccess={() => setMode('login')}
                  />
                </motion.div>
              )}
            </AnimatePresence>
          </CardContent>
        </Card>
      </motion.div>
    </div>
    </GradientThemeProvider>
  );
};