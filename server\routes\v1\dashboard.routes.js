// Dashboard Routes - Production Grade Implementation
import express from 'express';
import { authenticateToken, requireAdmin } from '../../middleware/auth.middleware.js';
import { validate } from '../../middleware/validation.middleware.js';
import { apiRateLimit } from '../../middleware/rateLimit.js';
import dashboardController from '../../controllers/dashboard.controller.js';
import Joi from 'joi';

const router = express.Router();

// Validation schemas
const dashboardQuerySchema = Joi.object({
  timeRange: Joi.string().valid('7d', '30d', '90d', '1y').default('30d'),
  includeInactive: Joi.boolean().default(false)
});

const activityFeedSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  type: Joi.string().valid('all', 'grievance', 'status_change', 'remark').default('all')
});

const chartsDataSchema = Joi.object({
  chartType: Joi.string().valid('status_trend', 'category_distribution', 'resolution_time').required(),
  timeRange: Joi.string().valid('7d', '30d', '90d', '1y').default('30d'),
  granularity: Joi.string().valid('day', 'week', 'month').default('day')
});

// User Dashboard Routes
router.get('/user-stats', 
  authenticateToken,
  apiRateLimit, // API rate limiting
  validate(dashboardQuerySchema, 'query'),
  dashboardController.getUserStats
);

router.get('/activity-feed',
  authenticateToken,
  apiRateLimit, // API rate limiting
  validate(activityFeedSchema, 'query'),
  dashboardController.getActivityFeed
);

router.get('/charts-data',
  authenticateToken,
  apiRateLimit, // API rate limiting
  validate(chartsDataSchema, 'query'),
  dashboardController.getChartsData
);

// Admin Dashboard Routes
router.get('/admin-stats',
  authenticateToken,
  requireAdmin,
  apiRateLimit, // API rate limiting
  validate(dashboardQuerySchema, 'query'),
  dashboardController.getAdminStats
);

router.get('/admin-analytics',
  authenticateToken,
  requireAdmin,
  apiRateLimit, // API rate limiting
  validate(Joi.object({
    metric: Joi.string().valid('user_activity', 'grievance_trends', 'performance_metrics').required(),
    timeRange: Joi.string().valid('7d', '30d', '90d', '1y').default('30d')
  }), 'query'),
  dashboardController.getAdminAnalytics
);

// Health check for dashboard services
router.get('/health',
  authenticateToken,
  dashboardController.getDashboardHealth
);

export default router;