// Grievance Routes v1
import express from 'express';
import <PERSON><PERSON> from 'joi';
import { validate, validateParams, validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken, requireAdmin } from '../../middleware/auth.middleware.js';
import { uploadRateLimit } from '../../middleware/rateLimit.js';
import { uploadSingle } from '../../middleware/fileUpload.middleware.js';
import grievanceController from '../../controllers/grievance.controller.js';

const router = express.Router();

// Validation schemas
const createGrievanceSchema = Joi.object({
  title: Joi.string().min(10).max(100).required(),
  description: Joi.string().min(20).max(1000).required(),
  category: Joi.string().valid(
    'Infrastructure', 'Sanitation', 'Utilities', 'Transportation', 
    'Public Safety', 'Healthcare', 'Education', 'Environment', 'Other'
  ).required(),
  subCategory: Joi.string().max(50).optional(),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent', 'Normal').default('medium'),
  location: Joi.object({
    address: Joi.string().max(200).optional(),
    city: Joi.string().max(50).optional(),
    district: Joi.string().max(50).optional(),
    postalCode: Joi.string().max(10).optional()
  }).optional()
});

const updateStatusSchema = Joi.object({
  newStatus: Joi.string().valid(
    'submitted', 'in_review', 'in_progress', 'assigned', 'resolved', 'rejected', 'closed'
  ).required(),
  reason: Joi.string().max(200).optional()
});

const reopenGrievanceSchema = Joi.object({
  reopenedReason: Joi.string().min(10).max(200).required()
});

const updateGrievanceSchema = Joi.object({
  title: Joi.string().min(10).max(100).optional(),
  description: Joi.string().min(20).max(1000).optional(),
  category: Joi.string().valid(
    'Infrastructure', 'Sanitation', 'Utilities', 'Transportation', 
    'Public Safety', 'Healthcare', 'Education', 'Environment', 'Other'
  ).optional(),
  subCategory: Joi.string().max(50).optional(),
  priority: Joi.string().valid('low', 'medium', 'high', 'urgent', 'Normal').optional(),
  location: Joi.object({
    address: Joi.string().max(200).optional(),
    city: Joi.string().max(50).optional(),
    district: Joi.string().max(50).optional(),
    postalCode: Joi.string().max(10).optional()
  }).optional()
});

const grievanceQuerySchema = Joi.object({
  q: Joi.string().min(2).max(100).optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  status: Joi.alternatives().try(
    Joi.string().valid('all', 'submitted', 'in_review', 'in_progress', 'assigned', 'resolved', 'rejected'),
    Joi.string().pattern(/^(submitted|in_review|in_progress|assigned|resolved|rejected)(,(submitted|in_review|in_progress|assigned|resolved|rejected))*$/)
  ).optional(),
  category: Joi.alternatives().try(
    Joi.string().valid('all', 'Infrastructure', 'Sanitation', 'Utilities', 'Transportation', 'Public Safety', 'Healthcare', 'Education', 'Environment', 'Other'),
    Joi.string().pattern(/^(Infrastructure|Sanitation|Utilities|Transportation|Public Safety|Healthcare|Education|Environment|Other)(,(Infrastructure|Sanitation|Utilities|Transportation|Public Safety|Healthcare|Education|Environment|Other))*$/)
  ).optional(),
  priority: Joi.string().valid('all', 'low', 'medium', 'high', 'urgent', 'Normal').optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
  assignedTo: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional(),
  sortBy: Joi.string().valid('submittedAt', 'lastUpdatedAt', 'status', 'priority', 'title').default('submittedAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc'),
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).optional()
});

const objectIdSchema = Joi.object({
  id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

// Routes

// Create grievance
router.post('/',
  authenticateToken,
  uploadRateLimit,
  validate(createGrievanceSchema),
  grievanceController.createGrievance
);

// Get user's grievances with advanced search
router.get('/',
  authenticateToken,
  validateQuery(grievanceQuerySchema),
  grievanceController.getUserGrievances
);

// Get dashboard statistics
router.get('/stats',
  authenticateToken,
  grievanceController.getDashboardStats
);

// Get all grievances with advanced search (admin only)
router.get('/admin/all',
  authenticateToken,
  requireAdmin,
  validateQuery(grievanceQuerySchema),
  grievanceController.getAllGrievances
);

// Get grievance by ID
router.get('/:id',
  authenticateToken,
  validateParams(objectIdSchema),
  grievanceController.getGrievanceById
);

// Update grievance (edit)
router.patch('/:id',
  authenticateToken,
  validateParams(objectIdSchema),
  validate(updateGrievanceSchema),
  grievanceController.updateGrievance
);

// Get grievance edit history
router.get('/:id/history',
  authenticateToken,
  validateParams(objectIdSchema),
  grievanceController.getGrievanceEditHistory
);

// Update grievance status (admin only)
router.put('/:id/status',
  authenticateToken,
  requireAdmin,
  validateParams(objectIdSchema),
  validate(updateStatusSchema),
  grievanceController.updateGrievanceStatus
);

// Reopen grievance
router.post('/:id/reopen',
  authenticateToken,
  validateParams(objectIdSchema),
  validate(reopenGrievanceSchema),
  grievanceController.reopenGrievance
);

// Upload attachment to grievance - Production-Grade Security
router.post('/:id/attachments',
  authenticateToken,
  uploadRateLimit,
  validateParams(objectIdSchema),
  ...uploadSingle('attachment'),
  grievanceController.uploadGrievanceAttachment
);

// Download grievance attachment - Secure Access Control
router.get('/:grievanceId/attachments/:fileId',
  authenticateToken,
  validateParams(Joi.object({
    grievanceId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
    fileId: Joi.string().required()
  })),
  grievanceController.downloadGrievanceAttachment
);

// Delete grievance attachment - Secure Deletion
router.delete('/:grievanceId/attachments/:fileId',
  authenticateToken,
  validateParams(Joi.object({
    grievanceId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
    fileId: Joi.string().required()
  })),
  grievanceController.deleteGrievanceAttachment
);

export default router;