// Unified API Client
// Single source of truth for all frontend-to-backend requests

import { apiConfig } from './apiConfig';

interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
}

interface RequestOptions extends RequestInit {
  skipAuth?: boolean;
  skipBypass?: boolean;
}

class ApiClient {
  private async getHeaders(options: RequestOptions = {}): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...options.headers as Record<string, string>
    };

    // Add auth token if available and not skipped
    if (!options.skipAuth) {
      const token = localStorage.getItem('auth-token');
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }
    }

    // Add bypass header if rate limiting is disabled and not skipped
    if (!options.skipBypass) {
      const rateLimitDisabled = localStorage.getItem('rateLimitDisabled') === 'true';
      if (rateLimitDisabled) {
        headers['X-Bypass-Rate-Limit'] = 'true';
      }
    }

    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    let data: any;
    
    try {
      data = await response.json();
    } catch (error) {
      // Handle non-JSON responses
      data = { message: response.statusText };
    }

    if (!response.ok) {
      // Handle rate limit errors specifically
      if (response.status === 429) {
        const rateLimitDisabled = localStorage.getItem('rateLimitDisabled') === 'true';
        if (rateLimitDisabled) {
          throw new Error('Request failed (rate limit bypass active)');
        } else {
          throw new Error(data.error || 'Too many requests. Please try again later.');
        }
      }

      // Handle other HTTP errors
      const errorMessage = data.error || data.message || `HTTP ${response.status}: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    return {
      success: true,
      data: data.data || data,
      message: data.message
    };
  }

  async request<T = any>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    try {
      await apiConfig.initialize();
      const baseUrl = apiConfig.getBaseUrl();
      const headers = await this.getHeaders(options);

      const response = await fetch(`${baseUrl}${endpoint}`, {
        ...options,
        headers
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      throw error instanceof Error ? error : new Error('Network error occurred');
    }
  }

  // Convenience methods for common HTTP verbs
  async get<T = any>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'GET' });
  }

  async post<T = any>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async put<T = any>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined
    });
  }

  async delete<T = any>(endpoint: string, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { ...options, method: 'DELETE' });
  }

  async patch<T = any>(endpoint: string, data?: any, options: RequestOptions = {}): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
