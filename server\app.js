// Optimized Express App with Performance Middleware
import express from 'express';
import { createServer } from 'http';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import { connectDB } from './config/database.js';
import { logger, requestLoggingMiddleware, errorLoggingMiddleware } from './middleware/logging.middleware.js';
import { errorHandler, requestIdMiddleware, notFoundHandler } from './middleware/error.middleware.js';
import { globalRateLimit, rateLimitBypassInfoMiddleware } from './middleware/rateLimit.js';
import WebSocketManager from './websocket.js';
import NotificationTemplate from './models/NotificationTemplate.js';

// Performance middleware
import {
  compressionMiddleware,
  requestTimingMiddleware,
  memoryMonitoringMiddleware,
  cacheControlMiddleware,
  requestSizeLimitMiddleware
} from './middleware/performance.middleware.js';

// Routes
import v1Routes from './routes/v1/index.js';

const app = express();
const server = createServer(app);

// Connect to database and initialize templates
connectDB().then(async () => {
  await NotificationTemplate.initializeDefaults();
  logger.info('Database connected and notification templates initialized');
});

// Initialize Redis for production scaling
if (process.env.NODE_ENV === 'production' && process.env.REDIS_URL) {
  import('./config/redis.js').then(async ({ default: redisManager }) => {
    const redisInitialized = await redisManager.initialize();
    if (redisInitialized) {
      logger.info('Redis initialized for production scaling');
    } else {
      logger.warn('Redis initialization failed, continuing without Redis');
    }
  }).catch(error => {
    logger.error('Failed to import Redis manager', { error: error.message });
  });
}

// Initialize WebSocket server
WebSocketManager.initialize(server).catch(error => {
  logger.error('Failed to initialize WebSocket server', { error: error.message });
});

// Initialize SLA monitoring cron job
if (process.env.NODE_ENV !== 'test') {
  import('node-cron').then(cron => {
    // Run SLA monitor every hour
    cron.default.schedule('0 * * * *', async () => {
      try {
        const { monitorSlaCompliance } = await import('./cron/slaMonitor.cjs');
        await monitorSlaCompliance();
      } catch (error) {
        logger.error('SLA monitoring cron job failed:', error);
      }
    });
    logger.info('SLA monitoring cron job scheduled');
  }).catch(error => {
    logger.error('Failed to initialize SLA monitoring:', error);
  });
}

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"]
    }
  }
}));

// CORS configuration
const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3002', 'http://localhost:3004', 'http://localhost:5173'];
app.use(cors({
  origin: (origin, callback) => {
    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS policy'));
    }
  },
  credentials: true
}));

// Performance middleware (order matters)
app.use(compressionMiddleware); // Compress responses
app.use(requestTimingMiddleware); // Time requests
app.use(memoryMonitoringMiddleware); // Monitor memory
app.use(cacheControlMiddleware); // Set cache headers
app.use(requestSizeLimitMiddleware); // Limit request size

// Unified rate limiting with built-in bypass support
app.use(globalRateLimit);
app.use(rateLimitBypassInfoMiddleware);

// Request ID and logging middleware
app.use(requestIdMiddleware);
app.use(requestLoggingMiddleware);

// Add simple request logging
app.use((req, res, next) => {
  console.log(`📥 [${new Date().toLocaleTimeString()}] ${req.method} ${req.url}`);
  next();
});

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// API routes
app.use('/api/v1', v1Routes);

// Health check endpoint
app.get('/health', (req, res) => {
  const memUsage = process.memoryUsage();
  console.log(`🏥 [${new Date().toLocaleTimeString()}] Health check requested`);
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    memory: {
      used: Math.round(memUsage.heapUsed / 1024 / 1024) + 'MB',
      total: Math.round(memUsage.heapTotal / 1024 / 1024) + 'MB'
    }
  });
});

// 404 handler
app.use('*', notFoundHandler);

// Error logging middleware
app.use(errorLoggingMiddleware);

// Error handling middleware (must be last)
app.use(errorHandler);

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

export { app, server };
export default app;