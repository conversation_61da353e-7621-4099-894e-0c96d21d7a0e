// Rate Limiting Bypass Middleware
import { logger } from './logging.middleware.js';

/**
 * Middleware to handle rate limiting bypass for development/testing
 * This middleware should be applied BEFORE any rate limiting middleware
 */
export const rateLimitBypassMiddleware = (req, res, next) => {
  // Check for bypass header
  const bypassHeader = req.get('X-Bypass-Rate-Limit');
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  // Only allow bypass in development environment or with specific header
  if (bypassHeader === 'true' && isDevelopment) {
    // Mark request as bypassed
    req.rateLimitBypassed = true;
    
    logger.info('Rate limiting bypassed for request', {
      path: req.path,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      bypassReason: 'X-Bypass-Rate-Limit header'
    });
  }
  
  next();
};

/**
 * Enhanced rate limiting middleware factory that respects bypass
 * @param {Function} rateLimitMiddleware - The original rate limit middleware
 * @returns {Function} Enhanced middleware that can be bypassed
 */
export const createBypassableRateLimit = (rateLimitMiddleware) => {
  return (req, res, next) => {
    // If request is marked as bypassed, skip rate limiting
    if (req.rateLimitBypassed) {
      logger.debug('Skipping rate limit due to bypass', {
        path: req.path,
        method: req.method,
        ip: req.ip
      });
      return next();
    }
    
    // Otherwise, apply the rate limiting middleware
    return rateLimitMiddleware(req, res, next);
  };
};

/**
 * Create a rate limiting configuration with bypass support
 * @param {Object} options - Rate limiting options
 * @returns {Object} Enhanced rate limiting configuration
 */
export const createRateLimitWithBypass = (options = {}) => {
  const originalSkip = options.skip || (() => false);
  
  return {
    ...options,
    skip: (req, res) => {
      // First check if request is bypassed
      if (req.rateLimitBypassed) {
        return true;
      }
      
      // Then check original skip condition
      return originalSkip(req, res);
    }
  };
};

/**
 * Middleware to add rate limit bypass information to response headers
 */
export const rateLimitBypassInfoMiddleware = (req, res, next) => {
  if (req.rateLimitBypassed) {
    res.setHeader('X-Rate-Limit-Bypassed', 'true');
    res.setHeader('X-Rate-Limit-Bypass-Reason', 'Development bypass enabled');
  }
  
  next();
};

export default {
  rateLimitBypassMiddleware,
  createBypassableRateLimit,
  createRateLimitWithBypass,
  rateLimitBypassInfoMiddleware
};
