// Unified Rate Limiting Middleware
// Single source of truth for all server-side rate limiting logic

import rateLimit from 'express-rate-limit';
import { logger } from './logging.middleware.js';
import { rateLimitConfig, RATE_LIMIT_TYPES } from '../config/rateLimitConfig.js';

/**
 * Simple bypass check - single place for all bypass logic
 * @param {Object} req - Express request object
 * @returns {boolean} - True if request should bypass rate limiting
 */
const shouldBypass = (req) => {
  const bypassHeader = req.get('X-Bypass-Rate-Limit');
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  return bypassHeader === 'true' && isDevelopment;
};

/**
 * Enhanced rate limiting middleware factory
 * @param {string} type - Rate limit type from RATE_LIMIT_TYPES
 * @param {Object} customOptions - Optional custom configuration
 * @returns {Function} Express middleware function
 */
export const createRateLimit = (type, customOptions = {}) => {
  // Get base configuration for the specified type
  const baseConfig = rateLimitConfig[type];
  
  if (!baseConfig) {
    throw new Error(`Invalid rate limit type: ${type}. Must be one of: ${Object.values(RATE_LIMIT_TYPES).join(', ')}`);
  }

  // Merge base config with custom options
  const config = {
    ...baseConfig,
    ...customOptions,
    // Enhanced handler with bypass support
    handler: (req, res) => {
      logger.warn('Rate limit exceeded', {
        type,
        ip: req.ip,
        path: req.path,
        method: req.method,
        userAgent: req.get('User-Agent'),
        bypassed: shouldBypass(req)
      });

      const message = customOptions.message || baseConfig.message;
      const retryAfter = Math.ceil((customOptions.windowMs || baseConfig.windowMs) / 1000);

      res.status(429).json({
        success: false,
        error: message.error,
        code: message.code,
        retryAfter,
        type
      });
    },
    // Enhanced skip function with bypass support
    skip: (req, res) => {
      // First check if request should be bypassed
      if (shouldBypass(req)) {
        logger.debug('Skipping rate limit due to bypass', {
          type,
          path: req.path,
          method: req.method,
          ip: req.ip
        });
        return true;
      }

      // Then check original skip condition if it exists
      const originalSkip = customOptions.skip || baseConfig.skip;
      return originalSkip ? originalSkip(req, res) : false;
    }
  };

  return rateLimit(config);
};

// Pre-configured rate limiters for common use cases
export const globalRateLimit = createRateLimit(RATE_LIMIT_TYPES.GLOBAL);
export const authRateLimit = createRateLimit(RATE_LIMIT_TYPES.AUTH);
export const apiRateLimit = createRateLimit(RATE_LIMIT_TYPES.API);
export const uploadRateLimit = createRateLimit(RATE_LIMIT_TYPES.UPLOAD);
export const adminRateLimit = createRateLimit(RATE_LIMIT_TYPES.ADMIN);

// Specific auth rate limiters with custom configurations
export const loginRateLimit = createRateLimit(RATE_LIMIT_TYPES.AUTH, {
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 login attempts per window
  message: {
    error: 'Too many login attempts. Please try again later.',
    code: 'LOGIN_RATE_LIMIT_EXCEEDED'
  }
});

export const registrationRateLimit = createRateLimit(RATE_LIMIT_TYPES.AUTH, {
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 registrations per hour
  message: {
    error: 'Too many registration attempts. Please try again later.',
    code: 'REGISTRATION_RATE_LIMIT_EXCEEDED'
  }
});

export const passwordResetRateLimit = createRateLimit(RATE_LIMIT_TYPES.AUTH, {
  windowMs: 60 * 1000, // 1 minute
  max: 1, // 1 password reset per minute
  message: {
    error: 'Too many password reset requests. Please wait before trying again.',
    code: 'PASSWORD_RESET_RATE_LIMIT_EXCEEDED'
  }
});

// Middleware to add bypass information to response headers
export const rateLimitBypassInfoMiddleware = (req, res, next) => {
  if (shouldBypass(req)) {
    res.setHeader('X-Rate-Limit-Bypassed', 'true');
    res.setHeader('X-Rate-Limit-Bypass-Reason', 'Development bypass enabled');
  }
  next();
};

// Export the bypass check function for external use
export { shouldBypass };

export default {
  createRateLimit,
  globalRateLimit,
  authRateLimit,
  apiRateLimit,
  uploadRateLimit,
  adminRateLimit,
  loginRateLimit,
  registrationRateLimit,
  passwordResetRateLimit,
  rateLimitBypassInfoMiddleware,
  shouldBypass,
  RATE_LIMIT_TYPES
};
