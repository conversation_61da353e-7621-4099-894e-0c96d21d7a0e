// Report Generation Routes
const express = require('express');
const { authenticateToken, requireAdmin } = require('../middleware/auth.middleware');
const { validateParams, validateQuery } = require('../middleware/validation.middleware');
const { uploadRateLimit } = require('../middleware/rateLimit.js');
const {
  generateGrievanceReport,
  generateUserActivityReport,
  generateSystemReport
} = require('../controllers/report.controller');
const Joi = require('joi');

const router = express.Router();

// Validation schemas
const grievanceReportSchema = Joi.object({
  grievanceId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

const grievanceReportQuerySchema = Joi.object({
  format: Joi.string().valid('pdf').default('pdf'),
  includeRemarks: Joi.boolean().default(true),
  includeTimeline: Joi.boolean().default(true)
});

const userActivityReportQuerySchema = Joi.object({
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
  format: Joi.string().valid('pdf').default('pdf')
});

const systemReportQuerySchema = Joi.object({
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().min(Joi.ref('startDate')).optional(),
  format: Joi.string().valid('pdf').default('pdf')
});

// Generate grievance report
router.get(
  '/grievance/:grievanceId',
  authenticateToken,
  uploadRateLimit, // Rate limit report generation
  validateParams(grievanceReportSchema),
  validateQuery(grievanceReportQuerySchema),
  generateGrievanceReport
);

// Generate user activity report (admin only)
router.get(
  '/user-activity',
  authenticateToken,
  requireAdmin,
  uploadRateLimit,
  validateQuery(userActivityReportQuerySchema),
  generateUserActivityReport
);

// Generate system analytics report (admin only)
router.get(
  '/system',
  authenticateToken,
  requireAdmin,
  uploadRateLimit,
  validateQuery(systemReportQuerySchema),
  generateSystemReport
);

module.exports = router;