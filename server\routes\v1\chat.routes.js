// Enhanced Chat Routes v1 with Ollama Integration
import express from 'express';
import <PERSON><PERSON> from 'joi';
import { validateBody, validateParams, validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken } from '../../middleware/auth.middleware.js';
import { uploadRateLimit } from '../../middleware/rateLimit.js';
import {
  startConversation,
  sendMessage,
  streamResponse,
  streamViaWebSocket,
  getConversationHistory,
  getUserConversations,
  deleteConversation,
  getServiceHealth
} from '../../controllers/chat.controller.js';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Validation schemas
const startConversationSchema = Joi.object({
  initialMessage: Joi.string().min(1).max(2000).required()
});

const sendMessageSchema = Joi.object({
  content: Joi.string().min(1).max(2000).required(),
  attachments: Joi.array().items(Joi.object({
    filename: Joi.string().required(),
    url: Joi.string().uri().required(),
    mimeType: Joi.string().required(),
    size: Joi.number().positive().required()
  })).max(5).optional()
});

const conversationIdSchema = Joi.object({
  conversationId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

const conversationQuerySchema = Joi.object({
  limit: Joi.number().integer().min(1).max(100).default(50),
  skip: Joi.number().integer().min(0).default(0)
});

const streamQuerySchema = Joi.object({
  message: Joi.string().min(1).max(2000).required()
});

const streamWebSocketSchema = Joi.object({
  message: Joi.string().min(1).max(2000).required(),
  context: Joi.array().items(Joi.object({
    role: Joi.string().valid('user', 'ai').required(),
    content: Joi.string().required(),
    timestamp: Joi.date().optional()
  })).max(10).optional()
});

const userConversationsQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10)
});

// Routes

// Start new conversation with AI
router.post('/conversations',
  uploadRateLimit, // Rate limit chat creation
  validateBody(startConversationSchema),
  startConversation
);

// Send message to conversation
router.post('/conversations/:conversationId/messages',
  uploadRateLimit, // Rate limit message sending
  validateParams(conversationIdSchema),
  validateBody(sendMessageSchema),
  sendMessage
);

// Stream AI response (Server-Sent Events)
router.get('/conversations/:conversationId/stream',
  validateParams(conversationIdSchema),
  validateQuery(streamQuerySchema),
  streamResponse
);

// Stream AI response via WebSocket (for real-time chat)
router.post('/conversations/:conversationId/stream',
  uploadRateLimit, // Rate limit streaming requests
  validateParams(conversationIdSchema),
  validateBody(streamWebSocketSchema),
  streamViaWebSocket
);

// Get conversation history
router.get('/conversations/:conversationId/messages',
  validateParams(conversationIdSchema),
  validateQuery(conversationQuerySchema),
  getConversationHistory
);

// Get user's conversations
router.get('/conversations',
  validateQuery(userConversationsQuerySchema),
  getUserConversations
);

// Delete conversation
router.delete('/conversations/:conversationId',
  validateParams(conversationIdSchema),
  deleteConversation
);

// Get Ollama service health status
router.get('/health',
  getServiceHealth
);

export default router;