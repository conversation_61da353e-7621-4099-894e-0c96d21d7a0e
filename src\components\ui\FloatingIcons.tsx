import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Avatar, AvatarFallback } from './avatar';
import { Button } from './button';
import { PersonIcon, GearIcon, ExitIcon, ChevronDownIcon } from '@radix-ui/react-icons';
import { Bell, Activity } from 'lucide-react';
import { useGradientTheme } from '../../providers/GradientThemeProvider';

interface FloatingIconsProps {
  onActivityToggle: (isVisible: boolean) => void;
  isActivityVisible: boolean;
  isNotificationOpen: boolean;
  setIsNotificationOpen: (open: boolean) => void;
  isProfileOpen: boolean;
  setIsProfileOpen: (open: boolean) => void;
  onLogout: () => void;
}

export const FloatingIcons: React.FC<FloatingIconsProps> = ({ 
  onActivityToggle, 
  isActivityVisible,
  isNotificationOpen,
  setIsNotificationOpen,
  isProfileOpen,
  setIsProfileOpen,
  onLogout
}) => {
  const { themeConfig } = useGradientTheme();

  const notifications = [
    { id: 1, title: 'Grievance Updated', message: 'Your grievance #GR-001 has been resolved', time: '2 min ago' },
    { id: 2, title: 'New Message', message: 'Admin replied to your query', time: '1 hour ago' },
    { id: 3, title: 'System Update', message: 'Platform maintenance scheduled', time: '3 hours ago' }
  ];

  return (
    <div className="flex items-center gap-3" onClick={(e) => e.stopPropagation()}>
      {/* Activity Toggle */}
      <motion.button
        className="p-3 rounded-2xl backdrop-blur-md border transition-all relative"
        style={{
          background: `linear-gradient(135deg, ${themeConfig.accentColor}15, rgba(255, 255, 255, 0.05))`,
          borderColor: `${themeConfig.accentColor}30`,
          boxShadow: `0 8px 32px ${themeConfig.accentColor}20, 0 0 40px ${themeConfig.accentColor}15`
        }}
        onClick={() => onActivityToggle(!isActivityVisible)}
        whileHover={{ 
          scale: 1.08,
          rotate: [0, -2, 2, 0],
          background: `linear-gradient(135deg, ${themeConfig.accentColor}25, rgba(255, 255, 255, 0.08))`,
          boxShadow: `0 12px 40px ${themeConfig.accentColor}30, 0 0 60px ${themeConfig.accentColor}25`,
          transition: { type: "spring", stiffness: 400, damping: 10 }
        }}
        whileTap={{ 
          scale: 0.92,
          rotate: 5,
          transition: { type: "spring", stiffness: 600, damping: 15 }
        }}
        animate={{
          boxShadow: isActivityVisible 
            ? [`0 8px 32px ${themeConfig.accentColor}20`, `0 12px 40px ${themeConfig.accentColor}30`, `0 8px 32px ${themeConfig.accentColor}20`]
            : '0 8px 32px rgba(0, 0, 0, 0.1)',
          rotate: isActivityVisible ? [0, 2, -2, 0] : 0
        }}
        transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
      >
        <Activity
          className="w-5 h-5"
          style={{
            color: isActivityVisible ? themeConfig.accentColor : '#ffffff',
            filter: isActivityVisible 
              ? `drop-shadow(0 0 12px ${themeConfig.accentColor}80) drop-shadow(0 0 24px ${themeConfig.accentColor}50)` 
              : 'drop-shadow(0 0 8px rgba(255, 255, 255, 0.4))'
          }}
        />
      </motion.button>

      {/* Notifications */}
      <div className="relative">
        <motion.button
          className="p-3 rounded-2xl backdrop-blur-md border transition-all relative"
          style={{
            background: `linear-gradient(135deg, #f59e0b15, rgba(255, 255, 255, 0.05))`,
            borderColor: '#f59e0b30',
            boxShadow: '0 8px 32px #f59e0b20, 0 0 40px #f59e0b15'
          }}
          onClick={() => {
            setIsProfileOpen(false);
            setIsNotificationOpen(!isNotificationOpen);
          }}
          whileHover={{ 
            scale: 1.08,
            rotate: [0, 3, -3, 0],
            y: [-1, -3, -1],
            background: 'linear-gradient(135deg, #f59e0b25, rgba(255, 255, 255, 0.08))',
            boxShadow: '0 12px 40px #f59e0b30, 0 0 60px #f59e0b25',
            transition: { type: "spring", stiffness: 400, damping: 10 }
          }}
          whileTap={{ 
            scale: 0.92,
            rotate: -5,
            transition: { type: "spring", stiffness: 600, damping: 15 }
          }}
          animate={{
            boxShadow: ['0 8px 32px #f59e0b20', '0 12px 40px #f59e0b30', '0 8px 32px #f59e0b20'],
            y: [0, -1, 0],
            rotate: [0, 1, -1, 0]
          }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        >
          <Bell
            className="w-5 h-5"
            style={{
              color: '#f59e0b',
              filter: 'drop-shadow(0 0 12px #f59e0b80) drop-shadow(0 0 24px #f59e0b50)'
            }}
          />
          <motion.span 
            className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold text-white"
            style={{ backgroundColor: themeConfig.accentColor }}
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ type: "spring", stiffness: 200 }}
          >
            3
          </motion.span>
        </motion.button>

        {/* Notification Dropdown */}
        <AnimatePresence>
          {isNotificationOpen && (
            <motion.div
              className="absolute top-full right-0 mt-2 w-80 sm:w-96 rounded-xl p-4 backdrop-blur-xl border z-50"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)'
              }}
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <h3 className="font-semibold mb-3 text-white">Notifications</h3>
              <div className="space-y-3">
                {notifications.map((notification, index) => (
                  <motion.div
                    key={notification.id}
                    className="p-3 rounded-lg cursor-pointer transition-all duration-200 border"
                    style={{
                      background: 'rgba(255, 255, 255, 0.03)',
                      borderColor: 'rgba(255, 255, 255, 0.1)'
                    }}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ backgroundColor: 'rgba(255, 255, 255, 0.08)' }}
                  >
                    <div className="font-medium text-sm text-white">{notification.title}</div>
                    <div className="text-xs opacity-70 mt-1 text-white">{notification.message}</div>
                    <div className="text-xs opacity-50 mt-1 text-white">{notification.time}</div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* Profile */}
      <div className="relative">
        <motion.button
          className="flex items-center gap-2 px-3 py-2 rounded-2xl backdrop-blur-md border transition-all"
          style={{
            background: `linear-gradient(135deg, #8b5cf615, rgba(255, 255, 255, 0.05))`,
            borderColor: '#8b5cf630',
            boxShadow: '0 8px 32px #8b5cf620, 0 0 40px #8b5cf615'
          }}
          onClick={() => {
            setIsNotificationOpen(false);
            setIsProfileOpen(!isProfileOpen);
          }}
          whileHover={{ 
            scale: 1.05,
            rotate: [0, 1, -1, 0],
            x: [0, 2, -2, 0],
            boxShadow: '0 12px 40px #8b5cf630, 0 0 60px #8b5cf625',
            transition: { type: "spring", stiffness: 350, damping: 12 }
          }}
          whileTap={{ 
            scale: 0.95,
            rotate: 3,
            transition: { type: "spring", stiffness: 500, damping: 18 }
          }}
          animate={{
            boxShadow: ['0 8px 32px #8b5cf620', '0 12px 40px #8b5cf630', '0 8px 32px #8b5cf620'],
            scale: [1, 1.01, 1],
            x: [0, 1, -1, 0]
          }}
          transition={{ duration: 1.8, repeat: Infinity, ease: "easeInOut" }}
        >
          <Avatar className="h-8 w-8">
            <AvatarFallback
              className="text-white font-bold text-sm"
              style={{
                background: `linear-gradient(135deg, ${themeConfig.accentColor}, ${themeConfig.accentColor}80)`,
                boxShadow: `0 0 20px ${themeConfig.accentColor}40`
              }}
            >
              U
            </AvatarFallback>
          </Avatar>
          <motion.div
            animate={{ rotate: isProfileOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            <ChevronDownIcon className="w-4 h-4 text-white" />
          </motion.div>
        </motion.button>

        {/* Profile Dropdown */}
        <AnimatePresence>
          {isProfileOpen && (
            <motion.div
              className="absolute top-full right-0 mt-2 w-48 sm:w-56 rounded-xl p-2 backdrop-blur-xl border z-50"
              style={{
                background: 'rgba(255, 255, 255, 0.05)',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                boxShadow: '0 20px 60px rgba(0, 0, 0, 0.2)'
              }}
              initial={{ opacity: 0, scale: 0.95, y: -10 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: -10 }}
              transition={{ duration: 0.2 }}
            >
              <Button
                variant="ghost"
                className="w-full flex items-center gap-2 px-3 py-2 rounded-lg justify-start text-white hover:bg-white/10"
              >
                <PersonIcon className="w-4 h-4" />
                <span className="text-sm">Profile</span>
              </Button>
              <Button
                variant="ghost"
                className="w-full flex items-center gap-2 px-3 py-2 rounded-lg justify-start text-white hover:bg-white/10"
              >
                <GearIcon className="w-4 h-4" />
                <span className="text-sm">Settings</span>
              </Button>
              <div className="h-px bg-white/10 my-2" />
              <Button
                variant="ghost"
                className="w-full flex items-center gap-2 px-3 py-2 rounded-lg justify-start text-red-400 hover:bg-red-500/10"
                onClick={onLogout}
              >
                <ExitIcon className="w-4 h-4" />
                <span className="text-sm">Logout</span>
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};