import React, { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { Label } from "../../../components/ui/label";
import { Alert, AlertDescription } from "../../../components/ui/alert";
import { PasswordStrengthMeter } from "../../../components/common/PasswordStrengthMeter";
import { authService } from "../services/AuthService";
import { useTheme } from "../../../providers/ThemeProvider";
import { Mail, ArrowLeft, Eye, EyeOff, Lock } from "lucide-react";
import "../../../styles/tooltip.css";
import "../../../styles/autofill-fix.css";

interface ForgotPasswordFormProps {
  onBack: () => void;
}

export const ForgotPasswordForm: React.FC<ForgotPasswordFormProps> = ({
  onBack,
}) => {
  const [gmail, setGmail] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");
  const [resetMethod, setResetMethod] = useState<"email" | "direct">("email");
  const { themeConfig } = useTheme();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@gmail\.com$/;
    return emailRegex.test(email);
  };

  const isFormValid = () => {
    if (!gmail) return false;
    if (resetMethod === "direct") {
      return newPassword && confirmPassword && newPassword === confirmPassword;
    }
    return true;
  };

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
    setMessage("");

    if (!gmail) {
      setError("Gmail address is required");
      return;
    }

    if (!validateEmail(gmail)) {
      setError("Please enter a valid Gmail address");
      return;
    }

    if (resetMethod === "direct") {
      if (!newPassword) {
        setError("New password is required");
        return;
      }
      if (!confirmPassword) {
        setError("Please confirm your password");
        return;
      }
      if (newPassword !== confirmPassword) {
        setError("Passwords do not match");
        return;
      }
    }

    setIsSubmitting(true);

    try {
      // Artificial delays removed - let server handle timing naturally

      const response = await authService.forgotPassword({ gmail });

      if (response.success) {
        setMessage(
          resetMethod === "email"
            ? "Password reset instructions have been sent to your Gmail address."
            : "Password updated successfully."
        );
      } else {
        // Handle specific error cases
        if (
          response.message?.includes("not found") ||
          response.message?.includes("does not exist")
        ) {
          setError(
            "No account found with this email address. Please check your email or create a new account."
          );
        } else {
          setError(
            response.message || "Failed to process password reset request."
          );
        }
      }
    } catch (error: any) {
      let errorMessage = "An error occurred. Please try again.";

      if (
        error.message?.includes("Network") ||
        error.message?.includes("timeout")
      ) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else if (error.message?.includes("server")) {
        errorMessage = "Server error. Please try again later.";
      }

      setError(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <motion.div
      className="space-y-6"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      {message && (
        <Alert>
          <AlertDescription>{message}</AlertDescription>
        </Alert>
      )}

      {error && (
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="space-y-8">
        {/* Reset Method Selection */}
        <div className="flex justify-center gap-4">
          <motion.button
            type="button"
            onClick={() => setResetMethod("email")}
            whileHover={{ scale: 1.05 }}
            className={`px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-200 ${
              resetMethod === "email" ? "glass-light" : "glass"
            }`}
            style={{
              color:
                resetMethod === "email"
                  ? `rgb(${themeConfig.colors.primary})`
                  : `rgb(${themeConfig.colors.foreground})`,
              boxShadow:
                resetMethod === "email"
                  ? `0 0 10px rgb(${themeConfig.colors.primary} / 0.3)`
                  : "none",
            }}
          >
            Email Verification
          </motion.button>
          <motion.button
            type="button"
            onClick={() => setResetMethod("direct")}
            whileHover={{ scale: 1.05 }}
            className={`px-4 py-2 rounded-2xl text-sm font-medium transition-all duration-200 ${
              resetMethod === "direct" ? "glass-light" : "glass"
            }`}
            style={{
              color:
                resetMethod === "direct"
                  ? `rgb(${themeConfig.colors.primary})`
                  : `rgb(${themeConfig.colors.foreground})`,
              boxShadow:
                resetMethod === "direct"
                  ? `0 0 10px rgb(${themeConfig.colors.primary} / 0.3)`
                  : "none",
            }}
          >
            Direct Password Change
          </motion.button>
        </div>

        <form onSubmit={onSubmit} className="space-y-6">
          {/* Email Field - Always visible */}
          <div className="space-y-4">
            <motion.div
              animate={{
                color: `rgb(${themeConfig.colors.accent})`,
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                repeatType: "reverse",
              }}
            >
              <Label htmlFor="gmail" className="text-base font-semibold">
                Gmail Address
              </Label>
            </motion.div>
            <div className="relative group">
              <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input
                id="gmail"
                type="email"
                placeholder="Enter your email"
                value={gmail}
                onChange={(e) => setGmail(e.target.value)}
                className={`pl-12 pr-4 py-4 text-base h-12 transition-all duration-200 ${
                  error && error.includes("email")
                    ? "border-red-500 animate-shake"
                    : ""
                }`}
                disabled={isSubmitting}
              />
              <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
                Enter the Gmail address associated with your account
              </div>
            </div>
            {error && error.includes("Gmail") && (
              <div className="glass-light px-3 py-2 rounded-xl border border-destructive/20">
                <p className="text-sm text-destructive font-medium">{error}</p>
              </div>
            )}
          </div>

          {/* Password Fields - Only for direct method */}
          {resetMethod === "direct" && (
            <>
              <div className="space-y-4">
                <motion.div
                  animate={{
                    color: `rgb(${themeConfig.colors.secondary})`,
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    repeatType: "reverse",
                  }}
                >
                  <Label
                    htmlFor="newPassword"
                    className="text-base font-semibold"
                  >
                    New Password
                  </Label>
                </motion.div>
                <div className="relative group">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="newPassword"
                    type={showNewPassword ? "text" : "password"}
                    placeholder="Enter new password"
                    value={newPassword}
                    onChange={(e) => setNewPassword(e.target.value)}
                    className="pl-12 pr-14 py-4 text-base h-12"
                    disabled={isSubmitting}
                  />
                  <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
                    Create a strong new password with uppercase, lowercase,
                    numbers, and special characters
                  </div>
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-xl transition-all duration-[25ms] hover:bg-primary/20 flex items-center justify-center focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        <motion.div
                          animate={{ rotateY: showNewPassword ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="flex items-center justify-center"
                        >
                          {showNewPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </motion.div>
                      </Button>
                    </motion.div>
                  </div>
                </div>
                {newPassword && (
                  <PasswordStrengthMeter password={newPassword} />
                )}
                {error &&
                  error.includes("password") &&
                  !error.includes("match") && (
                    <div className="glass-light px-3 py-2 rounded-xl border border-destructive/20">
                      <p className="text-sm text-destructive font-medium">
                        {error}
                      </p>
                    </div>
                  )}
              </div>

              <div className="space-y-4">
                <motion.div
                  animate={{
                    color: `rgb(${themeConfig.colors.primary})`,
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse",
                  }}
                >
                  <Label
                    htmlFor="confirmPassword"
                    className="text-base font-semibold"
                  >
                    Confirm New Password
                  </Label>
                </motion.div>
                <div className="relative group">
                  <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                  <Input
                    id="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    placeholder="Confirm new password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className="pl-12 pr-14 py-4 text-base h-12"
                    disabled={isSubmitting}
                  />
                  <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
                    Re-enter your new password to confirm it matches
                  </div>
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    <motion.div
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 rounded-xl transition-all duration-[25ms] hover:bg-primary/20 flex items-center justify-center focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary/50 focus-visible:ring-offset-0"
                        onClick={() =>
                          setShowConfirmPassword(!showConfirmPassword)
                        }
                      >
                        <motion.div
                          animate={{ rotateY: showConfirmPassword ? 180 : 0 }}
                          transition={{ duration: 0.3 }}
                          className="flex items-center justify-center"
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </motion.div>
                      </Button>
                    </motion.div>
                  </div>
                </div>
                {confirmPassword &&
                  newPassword &&
                  confirmPassword !== newPassword && (
                    <p className="text-sm text-destructive ml-1">
                      Passwords don't match
                    </p>
                  )}
                {error && error.includes("match") && (
                  <p className="text-sm text-destructive ml-1">{error}</p>
                )}
              </div>
            </>
          )}

          <div className="flex items-center justify-center gap-4 pt-4">
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
              className="glass w-12 h-12 rounded-full flex items-center justify-center shadow-lg"
            >
              <motion.button
                type="button"
                onClick={onBack}
                animate={{
                  color: [
                    `rgb(${themeConfig.colors.secondary})`,
                    `rgb(${themeConfig.colors.primary})`,
                    `rgb(${themeConfig.colors.secondary})`,
                  ],
                }}
                transition={{
                  duration: 2.5,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className="bg-transparent border-0 outline-none cursor-pointer p-2"
              >
                <ArrowLeft className="w-5 h-5" />
              </motion.button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="glass px-6 py-3 rounded-3xl shadow-lg"
            >
              <motion.button
                type="submit"
                disabled={isSubmitting || !isFormValid()}
                animate={{
                  color: [
                    `rgb(${themeConfig.colors.primary})`,
                    `rgb(${themeConfig.colors.accent})`,
                    `rgb(${themeConfig.colors.primary})`,
                  ],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
                className={`text-base font-semibold bg-transparent border-0 outline-none cursor-pointer ${
                  !isFormValid() ? "opacity-50 cursor-not-allowed" : ""
                }`}
              >
                {isSubmitting
                  ? resetMethod === "email"
                    ? "Sending..."
                    : "Updating..."
                  : resetMethod === "email"
                  ? "Send Reset Instructions"
                  : "Update Password"}
              </motion.button>
            </motion.div>
          </div>
        </form>
      </div>
    </motion.div>
  );
};
