// Enhanced Authentication Service
import User from '../models/User.js';
import PasswordResetToken from '../models/PasswordResetToken.js';
import SecurityEventService from './SecurityEventService.js';
import { emailService } from './emailService.js';
import CryptoUtils from '../utils/cryptoUtils.js';
import { logger } from '../middleware/logging.middleware.js';

class AuthService {
  // Security configuration
  static MAX_LOGIN_ATTEMPTS = 5;
  static LOCKOUT_DURATION = 60 * 60 * 1000; // 1 hour
  static RESET_TOKEN_EXPIRY = 30 * 60 * 1000; // 30 minutes

  // Handle login attempt with security checks
  static async handleLoginAttempt(email, password, ipAddress, userAgent) {
    try {
      const user = await User.findByEmail(email);
      if (!user) {
        // Log failed attempt for non-existent user
        await SecurityEventService.logEvent(null, 'LOGIN_FAILURE', {
          email,
          reason: 'User not found'
        }, ipAddress, userAgent);
        return {
          success: false,
          error: 'No account found with this email address',
          userNotFound: true
        };
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        await SecurityEventService.logEvent(user._id, 'LOGIN_BLOCKED', {
          reason: 'Account locked',
          lockedUntil: user.lockedUntil
        }, ipAddress, userAgent);
        return { 
          success: false, 
          error: 'Account locked. Try again later.',
          lockedUntil: user.lockedUntil
        };
      }

      // Verify password
      const isValidPassword = await User.verifyPassword(password, user.passwordHash);
      
      if (isValidPassword) {
        // Successful login
        await User.resetLoginAttempts(user._id);
        await User.updateLastLogin(user._id);
        
        await SecurityEventService.logEvent(user._id, 'LOGIN_SUCCESS', {
          previousAttempts: user.loginAttempts
        }, ipAddress, userAgent);

        return { success: true, user };
      } else {
        // Failed login
        const newAttempts = (user.loginAttempts || 0) + 1;
        let lockUntil = null;

        if (newAttempts >= this.MAX_LOGIN_ATTEMPTS) {
          lockUntil = new Date(Date.now() + this.LOCKOUT_DURATION);
        }

        await User.updateLoginAttempts(user._id, newAttempts, lockUntil, ipAddress);
        
        await SecurityEventService.logEvent(user._id, 'LOGIN_FAILURE', {
          attempts: newAttempts,
          reason: 'Invalid password'
        }, ipAddress, userAgent);

        if (lockUntil) {
          await SecurityEventService.logEvent(user._id, 'ACCOUNT_LOCKED', {
            attempts: newAttempts,
            lockedUntil: lockUntil
          }, ipAddress, userAgent);
        }

        // Check for suspicious activity
        await SecurityEventService.detectSuspiciousActivity(user._id, ipAddress);

        return {
          success: false,
          error: 'Invalid email or password. Please check your credentials.',
          wrongPassword: true,
          attemptsRemaining: this.MAX_LOGIN_ATTEMPTS - newAttempts,
          locked: !!lockUntil
        };
      }
    } catch (error) {
      logger.error('Login attempt failed', {
        error: error.message,
        email,
        ipAddress
      });
      throw error;
    }
  }

  // Generate and save password reset token
  static async generateAndSaveResetToken(userId) {
    try {
      const token = CryptoUtils.generatePasswordResetToken();
      const expiresAt = new Date(Date.now() + this.RESET_TOKEN_EXPIRY);

      const resetToken = await PasswordResetToken.create({
        userId,
        token,
        expiresAt
      });

      logger.info('Password reset token generated', {
        userId,
        tokenId: resetToken._id,
        expiresAt
      });

      return { token, expiresAt };
    } catch (error) {
      logger.error('Failed to generate reset token', {
        error: error.message,
        userId
      });
      throw error;
    }
  }

  // Validate password reset token
  static async validateResetToken(token) {
    try {
      const resetToken = await PasswordResetToken.findByToken(token);
      
      if (!resetToken) {
        return { valid: false, error: 'Invalid or expired token' };
      }

      return { valid: true, resetToken };
    } catch (error) {
      logger.error('Failed to validate reset token', {
        error: error.message,
        token: token.substring(0, 8) + '...'
      });
      return { valid: false, error: 'Token validation failed' };
    }
  }

  // Reset user password using token
  static async resetUserPassword(token, newPassword, ipAddress) {
    try {
      const validation = await this.validateResetToken(token);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      const { resetToken } = validation;
      const user = await User.findById(resetToken.userId);
      
      if (!user) {
        return { success: false, error: 'User not found' };
      }

      // Update password
      const updated = await User.updatePassword(resetToken.userId, newPassword);
      if (!updated) {
        return { success: false, error: 'Failed to update password' };
      }

      // Mark token as used
      await PasswordResetToken.markAsUsed(resetToken._id);

      // Clear all active sessions for security
      await User.clearAllSessions(resetToken.userId);

      // Log security event
      await SecurityEventService.logEvent(resetToken.userId, 'PASSWORD_RESET', {
        method: 'token',
        tokenId: resetToken._id
      }, ipAddress);

      logger.info('Password reset completed', {
        userId: resetToken.userId,
        tokenId: resetToken._id
      });

      return { success: true };
    } catch (error) {
      logger.error('Password reset failed', {
        error: error.message,
        token: token.substring(0, 8) + '...'
      });
      throw error;
    }
  }

  // Send password reset email
  static async sendPasswordResetEmail(email, ipAddress) {
    try {
      const user = await User.findByEmail(email);
      
      // Always return success to prevent email enumeration
      const response = { success: true, message: 'If an account exists, a reset link has been sent' };
      
      if (!user) {
        // Log attempt for non-existent email
        await SecurityEventService.logEvent(null, 'PASSWORD_RESET_REQUEST', {
          email,
          result: 'Email not found'
        }, ipAddress);
        return response;
      }

      // Generate reset token
      const { token } = await this.generateAndSaveResetToken(user._id);

      // Send email
      const emailResult = await emailService.sendPasswordResetEmail(
        user.gmail,
        token,
        user.fullName || 'User'
      );

      // Log the attempt
      await SecurityEventService.logEvent(user._id, 'PASSWORD_RESET_REQUEST', {
        emailSent: emailResult.success,
        emailError: emailResult.error || null
      }, ipAddress);

      if (!emailResult.success) {
        logger.error('Failed to send password reset email', {
          userId: user._id,
          error: emailResult.error
        });
      }

      return response;
    } catch (error) {
      logger.error('Password reset email process failed', {
        error: error.message,
        email
      });
      throw error;
    }
  }
}

export default AuthService;