// Enhanced Admin Routes v1
import express from 'express';
import <PERSON><PERSON> from 'joi';
import { validateBody, validateParams, validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken, requireAdmin } from '../../middleware/auth.middleware.js';
import { adminRateLimit } from '../../middleware/rateLimit.js';
import {
  getDashboardStats,
  getAllUsers,
  updateUserStatus,
  getAllGrievances,
  assignGrievance,
  updateGrievanceStatus,
  sendSystemNotification,
  exportGrievances,
  exportUsers,
  exportAnalytics
} from '../../controllers/admin.controller.js';
import slaController from '../../controllers/sla.controller.js';
import {
  createSetting,
  getSettings,
  getSettingByKey,
  updateSetting,
  deleteSetting,
  getSettingHistory,
  testSetting,
  bulkUpdateSettings,
  getSystemConfig
} from '../../controllers/systemSettings.controller.js';

const router = express.Router();

// Apply admin authentication and rate limiting to all routes
router.use(authenticateToken);
router.use(requireAdmin);
router.use(adminRateLimit);

// Validation schemas
const userStatusSchema = Joi.object({
  isActive: Joi.boolean().optional(),
  role: Joi.string().valid('user', 'admin', 'moderator').optional()
});

const assignGrievanceSchema = Joi.object({
  assignedTo: Joi.string().required(),
  reason: Joi.string().max(500).optional()
});

const updateGrievanceStatusSchema = Joi.object({
  status: Joi.string().valid(
    'submitted', 'in_review', 'in_progress', 'assigned', 'resolved', 'rejected', 'closed'
  ).required(),
  reason: Joi.string().max(500).optional(),
  estimatedResolution: Joi.date().greater('now').optional()
});

const systemNotificationSchema = Joi.object({
  title: Joi.string().min(1).max(100).required(),
  message: Joi.string().min(1).max(500).required(),
  priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
  targetUsers: Joi.alternatives().try(
    Joi.string().valid('all'),
    Joi.array().items(Joi.string().pattern(/^[0-9a-fA-F]{24}$/)).min(1)
  ).required()
});

const userQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  search: Joi.string().max(100).optional(),
  status: Joi.string().valid('all', 'active', 'inactive').default('all'),
  role: Joi.string().valid('all', 'user', 'admin', 'moderator').default('all'),
  sortBy: Joi.string().valid('createdAt', 'fullName', 'gmail', 'lastLogin').default('createdAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

const grievanceQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  search: Joi.string().max(100).optional(),
  status: Joi.string().valid('all', 'submitted', 'in_review', 'in_progress', 'assigned', 'resolved', 'rejected').default('all'),
  category: Joi.string().valid('all', 'Infrastructure', 'Sanitation', 'Utilities', 'Transportation', 'Public Safety', 'Healthcare', 'Education', 'Environment', 'Other').default('all'),
  priority: Joi.string().valid('all', 'low', 'medium', 'high', 'urgent', 'Normal').default('all'),
  assignedTo: Joi.string().optional(),
  dateFrom: Joi.date().optional(),
  dateTo: Joi.date().min(Joi.ref('dateFrom')).optional(),
  sortBy: Joi.string().valid('submittedAt', 'title', 'status', 'priority').default('submittedAt'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

const objectIdSchema = Joi.object({
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

const grievanceIdSchema = Joi.object({
  grievanceId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

const slaConfigSchema = Joi.object({
  category: Joi.string().valid(
    'Infrastructure', 'Sanitation', 'Utilities', 'Transportation', 
    'Public Safety', 'Healthcare', 'Education', 'Environment', 'Other'
  ).required(),
  status: Joi.string().valid(
    'submitted', 'in_review', 'in_progress', 'assigned'
  ).optional(),
  responseTimeHours: Joi.number().integer().min(1).max(168).required(),
  resolutionTimeHours: Joi.number().integer().min(1).max(720).required(),
  escalationLevels: Joi.array().items(
    Joi.object({
      level: Joi.number().integer().min(1).max(5).required(),
      timeAfterBreachHours: Joi.number().integer().min(1).max(168).required(),
      recipientRole: Joi.string().valid('admin', 'manager', 'supervisor').required()
    })
  ).optional(),
  businessHoursOnly: Joi.boolean().default(false)
});

const slaConfigIdSchema = Joi.object({
  id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

// System Settings validation schemas
const systemSettingSchema = Joi.object({
  key: Joi.string().min(1).max(100).pattern(/^[a-zA-Z][a-zA-Z0-9_]*$/).required(),
  value: Joi.alternatives().try(
    Joi.string(),
    Joi.number(),
    Joi.boolean(),
    Joi.array(),
    Joi.object()
  ).required(),
  type: Joi.string().valid('string', 'number', 'boolean', 'array', 'object').required(),
  description: Joi.string().max(500).optional()
});

const updateSettingSchema = Joi.object({
  value: Joi.alternatives().try(
    Joi.string(),
    Joi.number(),
    Joi.boolean(),
    Joi.array(),
    Joi.object()
  ).required(),
  description: Joi.string().max(500).optional()
});

const settingKeySchema = Joi.object({
  key: Joi.string().min(1).max(100).required()
});

const bulkSettingsSchema = Joi.object({
  settings: Joi.array().items(systemSettingSchema).min(1).max(50).required()
});

const settingsQuerySchema = Joi.object({
  type: Joi.string().valid('all', 'string', 'number', 'boolean', 'array', 'object').default('all'),
  search: Joi.string().max(100).optional()
});

// Dashboard and analytics
router.get('/stats', getDashboardStats);

// User management
router.get('/users', validateQuery(userQuerySchema), getAllUsers);
router.put('/users/:userId/status', 
  validateParams(objectIdSchema), 
  validateBody(userStatusSchema), 
  updateUserStatus
);

// Grievance management
router.get('/grievances', validateQuery(grievanceQuerySchema), getAllGrievances);
router.put('/grievances/:grievanceId/assign', 
  validateParams(grievanceIdSchema),
  validateBody(assignGrievanceSchema), 
  assignGrievance
);
router.put('/grievances/:grievanceId/status',
  validateParams(grievanceIdSchema),
  validateBody(updateGrievanceStatusSchema),
  updateGrievanceStatus
);

// System operations
router.post('/notifications/system', 
  validateBody(systemNotificationSchema), 
  sendSystemNotification
);

// SLA Configuration management
router.post('/sla-configs', 
  validateBody(slaConfigSchema), 
  slaController.createSlaConfig
);
router.get('/sla-configs', slaController.getSlaConfigs);
router.get('/sla-configs/:id', 
  validateParams(slaConfigIdSchema), 
  slaController.getSlaConfigById
);
router.put('/sla-configs/:id', 
  validateParams(slaConfigIdSchema),
  validateBody(slaConfigSchema), 
  slaController.updateSlaConfig
);
router.delete('/sla-configs/:id', 
  validateParams(slaConfigIdSchema), 
  slaController.deleteSlaConfig
);

// System Settings management
router.post('/settings', 
  validateBody(systemSettingSchema), 
  createSetting
);
router.get('/settings', 
  validateQuery(settingsQuerySchema), 
  getSettings
);
router.get('/settings/config', getSystemConfig);
router.get('/settings/:key', 
  validateParams(settingKeySchema), 
  getSettingByKey
);
router.put('/settings/:key', 
  validateParams(settingKeySchema),
  validateBody(updateSettingSchema), 
  updateSetting
);
router.delete('/settings/:key', 
  validateParams(settingKeySchema), 
  deleteSetting
);
router.get('/settings/:key/history', 
  validateParams(settingKeySchema), 
  getSettingHistory
);
router.post('/settings/test', 
  validateBody(systemSettingSchema), 
  testSetting
);
router.post('/settings/bulk', 
  validateBody(bulkSettingsSchema), 
  bulkUpdateSettings
);

// Data export endpoints
router.get('/export/grievances', exportGrievances);
router.get('/export/users', exportUsers);
router.get('/export/analytics', exportAnalytics);

export default router;