// Environment Configuration Management
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from parent directory
dotenv.config({ path: path.join(__dirname, '../../.env') });

const config = {
  // Server Configuration
  PORT: process.env.PORT || 3005,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Database Configuration
  MONGODB_URI: process.env.MONGODB_URI || 'mongodb://localhost:27017',
  DB_NAME: process.env.DB_NAME || 'civicassist',
  
  // Security Configuration
  JWT_SECRET: process.env.JWT_SECRET,
  JWT_EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',
  JWT_REFRESH_SECRET: process.env.JWT_REFRESH_SECRET,
  JWT_REFRESH_EXPIRES_IN: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  
  // Rate limiting configurations moved to server/config/rateLimitConfig.js
  
  // File Upload Configuration
  MAX_FILE_SIZE: parseInt(process.env.MAX_FILE_SIZE) || 50 * 1024 * 1024,
  UPLOAD_PATH: process.env.UPLOAD_PATH || './server/uploads',
  ALLOWED_FILE_TYPES: process.env.ALLOWED_FILE_TYPES?.split(',') || ['image/jpeg', 'image/png', 'application/pdf'],
  VIRUS_SCAN_ENABLED: process.env.VIRUS_SCAN_ENABLED === 'true',
  
  // CORS Configuration
  ALLOWED_ORIGINS: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:5173'],
  CORS_CREDENTIALS: process.env.CORS_CREDENTIALS === 'true',
  
  // Email Configuration
  EMAIL_HOST: process.env.EMAIL_HOST,
  EMAIL_PORT: parseInt(process.env.EMAIL_PORT) || 587,
  EMAIL_USER: process.env.EMAIL_USER,
  EMAIL_PASS: process.env.EMAIL_PASS,
  EMAIL_FROM: process.env.EMAIL_FROM || 'CivicAssist <<EMAIL>>',
  
  // External Services
  OLLAMA_BASE_URL: process.env.OLLAMA_BASE_URL || 'http://localhost:11434',
  REDIS_URL: process.env.REDIS_URL,
  
  // Security
  BCRYPT_ROUNDS: parseInt(process.env.BCRYPT_ROUNDS) || 12,
  SESSION_SECRET: process.env.SESSION_SECRET,
  COOKIE_SECURE: process.env.COOKIE_SECURE === 'true',
  COOKIE_HTTP_ONLY: process.env.COOKIE_HTTP_ONLY !== 'false',
  COOKIE_SAME_SITE: process.env.COOKIE_SAME_SITE || 'strict',
  
  // Logging
  LOG_LEVEL: process.env.LOG_LEVEL || 'info',
  LOG_FILE: process.env.LOG_FILE || './server/logs/app.log',
  ENABLE_REQUEST_LOGGING: process.env.ENABLE_REQUEST_LOGGING !== 'false',
  
  // Admin Configuration
  ADMIN_EMAIL: process.env.ADMIN_EMAIL,
  ADMIN_PASSWORD: process.env.ADMIN_PASSWORD,
  
  // Feature Flags
  ENABLE_CHAT: process.env.ENABLE_CHAT !== 'false',
  ENABLE_NOTIFICATIONS: process.env.ENABLE_NOTIFICATIONS !== 'false',
  ENABLE_FILE_UPLOAD: process.env.ENABLE_FILE_UPLOAD !== 'false',
  ENABLE_ANALYTICS: process.env.ENABLE_ANALYTICS !== 'false',
  
  // Monitoring
  HEALTH_CHECK_ENABLED: process.env.HEALTH_CHECK_ENABLED !== 'false',
  METRICS_ENABLED: process.env.METRICS_ENABLED !== 'false'
};

// Enhanced Validation
const requiredEnvVars = {
  production: ['JWT_SECRET', 'JWT_REFRESH_SECRET', 'SESSION_SECRET', 'MONGODB_URI'],
  development: ['JWT_SECRET', 'JWT_REFRESH_SECRET'],
  test: ['JWT_SECRET']
};

const envVarsForCurrentEnv = requiredEnvVars[config.NODE_ENV] || requiredEnvVars.development;
const missingEnvVars = envVarsForCurrentEnv.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error(`Missing required environment variables for ${config.NODE_ENV}:`, missingEnvVars);
  console.error('Please check your .env file and ensure all required variables are set.');
  if (config.NODE_ENV === 'production') {
    process.exit(1);
  } else {
    console.warn('Continuing with default values for development...');
  }
}

// Security validation
if (config.NODE_ENV === 'production') {
  if (config.JWT_SECRET && config.JWT_SECRET.length < 32) {
    console.error('JWT_SECRET must be at least 32 characters long in production');
    process.exit(1);
  }
  if (config.JWT_REFRESH_SECRET && config.JWT_REFRESH_SECRET.length < 32) {
    console.error('JWT_REFRESH_SECRET must be at least 32 characters long in production');
    process.exit(1);
  }
}

export default config;