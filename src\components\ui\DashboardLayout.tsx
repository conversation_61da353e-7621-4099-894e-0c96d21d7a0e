import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { User, Bell, Activity } from 'lucide-react';
import { useAuthStore } from '../../store/useAuthStore';
import { StatsCards } from './StatsCards';
import { ActivityFeed } from './ActivityFeed';
import { QuickActionsTile } from './QuickActionsTile';
import { HamburgerMenu } from './HamburgerMenu';
import { FloatingIcons } from './FloatingIcons';
import { FloatingNavDrawer } from './FloatingNavDrawer';
import { ThemeDrawer } from './ThemeDrawer';

export const DashboardLayout: React.FC = () => {
  const { logout } = useAuthStore();
  const [isNavOpen, setIsNavOpen] = useState(false);
  const [isActivityVisible, setIsActivityVisible] = useState(true);
  const [isThemeDrawerOpen, setIsThemeDrawerOpen] = useState(false);
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);

  const handleLogout = () => {
    logout();
  };

  const handleBackdropClick = () => {
    if (isThemeDrawerOpen) {
      setIsThemeDrawerOpen(false);
    } else if (isNavOpen) {
      setIsNavOpen(false);
    } else if (isNotificationOpen) {
      setIsNotificationOpen(false);
    } else if (isProfileOpen) {
      setIsProfileOpen(false);
    }
  };

  return (
    <div className="min-h-screen" onClick={handleBackdropClick}>
      <HamburgerMenu onToggle={setIsNavOpen} />
      
      <FloatingNavDrawer 
        isOpen={isNavOpen} 
        onClose={() => setIsNavOpen(false)}
        onThemeClick={() => setIsThemeDrawerOpen(true)}
        onLogout={handleLogout}
      />
      
      <ThemeDrawer 
        isOpen={isThemeDrawerOpen}
        onClose={() => setIsThemeDrawerOpen(false)}
      />


      
      {/* Main Content */}
      <motion.main
        className="pl-4 pr-4 py-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <div className="max-w-full mx-auto">
          {/* Single Main Layout Grid - 60/40 Split from top */}
          <div className={`grid gap-8 transition-all duration-150 ${isActivityVisible ? 'grid-cols-1 lg:grid-cols-5' : 'grid-cols-1'}`}>
            {/* Left Content Area - 60% */}
            <div className={`${isActivityVisible ? 'lg:col-span-3' : 'col-span-1'} space-y-6`}>
              {/* Dashboard Text and FloatingIcons at same horizontal level */}
              <div className="flex justify-between items-start">
                <motion.div
                  className="ml-12"
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <h1 
                    className="text-3xl font-bold mb-2 text-foreground"
                  >
                    Dashboard
                  </h1>
                  <p 
                    className="text-base opacity-70"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    Welcome back, User
                  </p>
                </motion.div>
                
                <div className="flex-shrink-0">
                  <FloatingIcons 
                    onActivityToggle={setIsActivityVisible}
                    isActivityVisible={isActivityVisible}
                    isNotificationOpen={isNotificationOpen}
                    setIsNotificationOpen={setIsNotificationOpen}
                    isProfileOpen={isProfileOpen}
                    setIsProfileOpen={setIsProfileOpen}
                    onLogout={handleLogout}
                  />
                </div>
              </div>
              
              {/* Quick Actions Tile */}
              <QuickActionsTile />
              
              {/* Stats Cards */}
              <StatsCards />
            </div>

            {/* Right Sidebar - Activity Feed at very top - 40% */}
            <AnimatePresence mode="wait">
              {isActivityVisible && (
                <motion.div
                  className="lg:col-span-2 mt-4"
                  initial={{ opacity: 0, x: 30, scale: 0.95 }}
                  animate={{ opacity: 1, x: 0, scale: 1 }}
                  exit={{ opacity: 0, x: 30, scale: 0.95 }}
                  transition={{ 
                    duration: 0.15, 
                    ease: "easeOut",
                    type: "spring",
                    stiffness: 400,
                    damping: 25
                  }}
                >
                  <ActivityFeed />
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.main>
    </div>
  );
};