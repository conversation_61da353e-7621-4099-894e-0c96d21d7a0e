import React, { useEffect } from 'react';
import { motion } from 'framer-motion';
import { Dialog, DialogContent } from '../../../components/ui/dialog';
import { CheckCircle, Sparkles, Mail } from 'lucide-react';
import { useGradientTheme } from '../../../providers/GradientThemeProvider';

interface RegistrationSuccessDialogProps {
  isOpen: boolean;
  onClose: () => void;
  theme?: any;
}

export const RegistrationSuccessDialog: React.FC<RegistrationSuccessDialogProps> = ({ isOpen, onClose, theme }) => {
  const { themeConfig } = useGradientTheme();
  const activeTheme = theme || themeConfig;

  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent 
        className="max-w-md mx-auto border-0 bg-transparent shadow-none p-0 overflow-hidden"
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.8, y: 50 }}
          animate={{ 
            opacity: 1, 
            scale: 1,
            y: 0
          }}
          exit={{ opacity: 0, scale: 0.8, y: 50 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="rounded-3xl p-8 text-center relative overflow-hidden"
          style={{
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            background: 'rgba(255, 255, 255, 0.05)',
            border: `1px solid ${activeTheme?.accentColor || '#10b981'}40`,
            boxShadow: `0 20px 60px rgba(0, 0, 0, 0.3), 0 0 30px ${activeTheme?.accentColor || '#10b981'}30`
          }}
        >
          {/* Animated background particles */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 rounded-full"
                style={{ 
                  backgroundColor: activeTheme?.accentColor || '#10b981',
                  opacity: 0.6
                }}
                initial={{ 
                  x: Math.random() * 400, 
                  y: Math.random() * 300,
                  scale: 0
                }}
                animate={{ 
                  x: Math.random() * 400, 
                  y: Math.random() * 300,
                  scale: [0, 1, 0]
                }}
                transition={{ 
                  duration: 2 + Math.random() * 2,
                  repeat: Infinity,
                  delay: i * 0.3
                }}
              />
            ))}
          </div>
          
          {/* Success Icon with glow */}
          <motion.div
            initial={{ scale: 0, rotate: -180 }}
            animate={{ scale: 1, rotate: 0 }}
            transition={{ delay: 0.2, duration: 0.6, type: "spring", bounce: 0.4 }}
            className="mb-6 relative"
          >
            <CheckCircle 
              className="w-20 h-20 mx-auto relative z-10"
              style={{
                color: activeTheme?.accentColor || '#10b981',
                filter: `drop-shadow(0 0 20px ${activeTheme?.accentColor || '#10b981'}60)`
              }}
            />
            <motion.div
              className="absolute inset-0 rounded-full"
              style={{ 
                background: `radial-gradient(circle, ${activeTheme?.accentColor || '#10b981'}40 0%, transparent 70%)`
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.5, 0.8, 0.5]
              }}
              transition={{
                duration: 2,
                repeat: Infinity
              }}
            />
          </motion.div>

          {/* Success Message */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.4 }}
            className="space-y-4 relative z-10"
          >
            <div className="flex items-center justify-center gap-3">
              <motion.h3 
                className="text-2xl font-bold text-white"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
              >
                Account Created!
              </motion.h3>
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.7 }}
              >
                <Sparkles 
                  className="h-6 w-6" 
                  style={{ color: activeTheme?.accentColor || '#fbbf24' }}
                />
              </motion.div>
            </div>
            
            <motion.p 
              className="text-white/80 text-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.6 }}
            >
              🎉 Welcome to CivicAssist! You can now sign in with your new account.
            </motion.p>
            
            <motion.div
              className="flex items-center justify-center gap-2 text-white/60 text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.8 }}
            >
              <Mail className="h-4 w-4" />
              <span>Check your email for a welcome message</span>
            </motion.div>
          </motion.div>

          {/* Pulse border animation */}
          <motion.div
            className="absolute inset-0 rounded-3xl pointer-events-none"
            style={{
              border: `2px solid ${activeTheme?.accentColor || '#10b981'}60`
            }}
            animate={{
              opacity: [0.3, 0.8, 0.3],
              scale: [1, 1.02, 1]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </motion.div>
      </DialogContent>
    </Dialog>
  );
};