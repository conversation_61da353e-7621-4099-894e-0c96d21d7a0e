// Enhanced Rate Limiting Middleware
import rateLimit from 'express-rate-limit';
import { logger } from './logging.middleware.js';
import { createRateLimitWithBypass } from './rateLimitBypass.middleware.js';

// Password reset rate limiting (stricter) with bypass support
const passwordResetRateLimit = rateLimit(createRateLimitWithBypass({
  windowMs: 60 * 1000, // 1 minute
  max: 1, // 1 request per minute per IP
  message: {
    error: 'Too many password reset requests. Please wait before trying again.',
    code: 'PASSWORD_RESET_RATE_LIMIT',
    retryAfter: 60
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Password reset rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body?.email,
      bypassed: req.rateLimitBypassed || false
    });

    res.status(429).json({
      success: false,
      error: 'Too many password reset requests. Please wait before trying again.',
      code: 'PASSWORD_RESET_RATE_LIMIT',
      retryAfter: 60
    });
  }
}));

// Login rate limiting (per IP) with bypass support
const loginRateLimit = rateLimit(createRateLimitWithBypass({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // 10 attempts per 15 minutes per IP
  message: {
    error: 'Too many login attempts. Please try again later.',
    code: 'LOGIN_RATE_LIMIT',
    retryAfter: 900
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Login rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body?.email,
      bypassed: req.rateLimitBypassed || false
    });

    res.status(429).json({
      success: false,
      error: 'Too many login attempts. Please try again later.',
      code: 'LOGIN_RATE_LIMIT',
      retryAfter: 900
    });
  }
}));

// Registration rate limiting with bypass support
const registrationRateLimit = rateLimit(createRateLimitWithBypass({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 registrations per hour per IP
  message: {
    error: 'Too many registration attempts. Please try again later.',
    code: 'REGISTRATION_RATE_LIMIT',
    retryAfter: 3600
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn('Registration rate limit exceeded', {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      email: req.body?.email,
      bypassed: req.rateLimitBypassed || false
    });

    res.status(429).json({
      success: false,
      error: 'Too many registration attempts. Please try again later.',
      code: 'REGISTRATION_RATE_LIMIT',
      retryAfter: 3600
    });
  }
}));

export {
  passwordResetRateLimit,
  loginRateLimit,
  registrationRateLimit
};