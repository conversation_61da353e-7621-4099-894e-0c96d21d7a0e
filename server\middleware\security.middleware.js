// Enhanced Security Middleware
import helmet from 'helmet';
import { securityHeaders } from '../config/security.js';
import { logger } from './logging.middleware.js';

// Rate limiting has been moved to server/middleware/rateLimit.js

// Enhanced security headers middleware
const securityHeadersMiddleware = (req, res, next) => {
  Object.entries(securityHeaders).forEach(([header, value]) => {
    res.setHeader(header, value);
  });
  next();
};

// Enhanced NoSQL injection prevention
const preventNoSQLInjection = (req, res, next) => {
  const sanitize = (obj, depth = 0) => {
    // Prevent deep recursion attacks
    if (depth > 10) {
      throw new Error('Input too deeply nested');
    }

    if (obj && typeof obj === 'object') {
      for (const key in obj) {
        // Prevent prototype pollution
        if (key === '__proto__' || key === 'constructor' || key === 'prototype') {
          delete obj[key];
          continue;
        }

        if (typeof obj[key] === 'string') {
          // Remove potential NoSQL injection patterns
          obj[key] = obj[key].replace(/[${}]/g, '');
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          sanitize(obj[key], depth + 1);
        }
      }
    }
  };

  try {
    sanitize(req.body);
    sanitize(req.query);
    sanitize(req.params);
    next();
  } catch (error) {
    logger.error('Input sanitization failed', {
      error: error.message,
      path: req.path,
      ip: req.ip
    });
    
    return res.status(400).json({
      error: 'Invalid input format',
      code: 'INVALID_INPUT'
    });
  }
};

// Content type validation
const validateContentType = (allowedTypes = ['application/json']) => {
  return (req, res, next) => {
    if (req.method === 'POST' || req.method === 'PUT' || req.method === 'PATCH') {
      const contentType = req.get('Content-Type');
      
      if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
        logger.warn('Invalid content type', {
          contentType,
          path: req.path,
          method: req.method,
          ip: req.ip
        });
        
        return res.status(400).json({
          error: 'Invalid content type',
          code: 'INVALID_CONTENT_TYPE',
          allowed: allowedTypes
        });
      }
    }
    next();
  };
};

// Request size limiting
const limitRequestSize = (maxSize = '10mb') => {
  return (req, res, next) => {
    const contentLength = req.get('Content-Length');
    
    if (contentLength) {
      const sizeInBytes = parseInt(contentLength);
      const maxSizeInBytes = typeof maxSize === 'string' 
        ? parseInt(maxSize) * (maxSize.includes('mb') ? 1024 * 1024 : 1024)
        : maxSize;
      
      if (sizeInBytes > maxSizeInBytes) {
        logger.warn('Request size exceeded', {
          size: sizeInBytes,
          maxSize: maxSizeInBytes,
          path: req.path,
          ip: req.ip
        });
        
        return res.status(413).json({
          error: 'Request entity too large',
          code: 'REQUEST_TOO_LARGE',
          maxSize: maxSize
        });
      }
    }
    
    next();
  };
};

// IP whitelist/blacklist middleware
const ipFilter = (whitelist = [], blacklist = []) => {
  return (req, res, next) => {
    const clientIP = req.ip || req.connection.remoteAddress;
    
    // Check blacklist first
    if (blacklist.length > 0 && blacklist.includes(clientIP)) {
      logger.warn('Blocked IP attempt', {
        ip: clientIP,
        path: req.path,
        method: req.method
      });
      
      return res.status(403).json({
        error: 'Access denied',
        code: 'IP_BLOCKED'
      });
    }
    
    // Check whitelist if configured
    if (whitelist.length > 0 && !whitelist.includes(clientIP)) {
      logger.warn('Non-whitelisted IP attempt', {
        ip: clientIP,
        path: req.path,
        method: req.method
      });
      
      return res.status(403).json({
        error: 'Access denied',
        code: 'IP_NOT_WHITELISTED'
      });
    }
    
    next();
  };
};

// Security audit logging
const securityAuditLog = (req, res, next) => {
  // Log security-sensitive operations
  const sensitiveEndpoints = ['/auth/', '/admin/', '/upload/', '/user/delete'];
  const isSensitive = sensitiveEndpoints.some(endpoint => req.path.includes(endpoint));
  
  if (isSensitive) {
    logger.info('Security-sensitive request', {
      path: req.path,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });
  }
  
  next();
};

export {
  securityHeadersMiddleware,
  preventNoSQLInjection,
  validateContentType,
  limitRequestSize,
  ipFilter,
  securityAuditLog
};