import { apiClient } from "./apiClient";
import { toast } from "../hooks/use-toast";

interface UserProfile {
  _id: string;
  gmail: string;
  fullName: string;
  phoneNumber?: string;
  address?: string;
  profilePictureUrl?: string;
  languagePreference?: string;
  notificationPreferences?: {
    statusChange: boolean;
    newGrievanceFiled: boolean;
    remarksAdded: boolean;
  };
}

interface UpdateProfileData {
  fullName?: string;
  phoneNumber?: string;
  address?: string;
  languagePreference?: string;
}

interface UserActivity {
  _id: string;
  type: string;
  description: string;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

interface UserStats {
  totalGrievances: number;
  resolvedGrievances: number;
  pendingGrievances: number;
  averageResolutionTime: number;
  lastLoginDate: string;
  accountCreatedDate: string;
}

class UserService {
  private async makeRequest(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<any> {
    try {
      const method = options.method || "GET";
      const body = options.body;

      let response;
      if (method === "GET") {
        response = await apiClient.get(endpoint);
      } else if (method === "POST") {
        const data = body ? JSON.parse(body as string) : undefined;
        response = await apiClient.post(endpoint, data);
      } else if (method === "PUT") {
        const data = body ? JSON.parse(body as string) : undefined;
        response = await apiClient.put(endpoint, data);
      } else if (method === "DELETE") {
        response = await apiClient.delete(endpoint);
      } else {
        const data = body ? JSON.parse(body as string) : undefined;
        response = await apiClient.request(endpoint, {
          method,
          body: body as string,
        });
      }

      return response.data;
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error("Network error occurred");
    }
  }

  // Token handling is now managed by the unified apiClient

  async getProfile(): Promise<UserProfile> {
    const response = await this.makeRequest("/users/profile");
    return response.user;
  }

  async updateProfile(data: UpdateProfileData): Promise<UserProfile> {
    const response = await this.makeRequest("/users/profile", {
      method: "PUT",
      body: JSON.stringify(data),
    });

    toast({
      title: "Profile Updated",
      description: "Your profile has been updated successfully.",
    });

    return response.user;
  }

  async uploadProfilePicture(
    file: File
  ): Promise<{ profilePictureUrl: string }> {
    const formData = new FormData();
    formData.append("profilePicture", file);

    const response = await this.makeRequest("/users/profile/upload-picture", {
      method: "POST",
      headers: {}, // Let browser set Content-Type for FormData
      body: formData,
    });

    toast({
      title: "Profile Picture Updated",
      description: "Your profile picture has been updated successfully.",
    });

    return response;
  }

  async getActivity(
    params: {
      page?: number;
      limit?: number;
      type?: string;
      startDate?: string;
      endDate?: string;
    } = {}
  ): Promise<{
    activities: UserActivity[];
    totalCount: number;
    currentPage: number;
    totalPages: number;
  }> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    const response = await this.makeRequest(`/users/activity?${queryParams}`);
    return response;
  }

  async getStats(days: number = 30): Promise<UserStats> {
    const response = await this.makeRequest(`/users/stats?days=${days}`);
    return response.stats;
  }

  async updatePreferences(preferences: {
    languagePreference?: string;
    notificationPreferences?: {
      statusChange?: boolean;
      newGrievanceFiled?: boolean;
      remarksAdded?: boolean;
    };
  }): Promise<void> {
    await this.makeRequest("/users/preferences", {
      method: "PUT",
      body: JSON.stringify(preferences),
    });

    toast({
      title: "Preferences Updated",
      description: "Your preferences have been saved successfully.",
    });
  }

  async deleteAccount(password: string): Promise<void> {
    await this.makeRequest("/users/delete-account", {
      method: "DELETE",
      body: JSON.stringify({ password }),
    });

    toast({
      title: "Account Deleted",
      description: "Your account has been permanently deleted.",
      variant: "destructive",
    });
  }

  async changePassword(
    currentPassword: string,
    newPassword: string
  ): Promise<void> {
    await this.makeRequest("/auth/change-password", {
      method: "PUT",
      body: JSON.stringify({
        currentPassword,
        newPassword,
      }),
    });

    toast({
      title: "Password Changed",
      description: "Your password has been updated successfully.",
    });
  }
}

export const userService = new UserService();
export type { UserProfile, UpdateProfileData, UserActivity, UserStats };
