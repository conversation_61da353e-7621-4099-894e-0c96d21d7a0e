// Upload Routes v1
import express from 'express';
import Jo<PERSON> from 'joi';
import { validateParams, validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken, requireAdmin } from '../../middleware/auth.middleware.js';
import { uploadRateLimit } from '../../middleware/rateLimit.js';
import uploadController from '../../controllers/upload.controller.js';

const router = express.Router();

// Validation schemas
const filenameSchema = Joi.object({
  filename: Joi.string().required()
});

const cleanupQuerySchema = Joi.object({
  daysOld: Joi.number().integer().min(1).max(365).default(30)
});

// Routes

// Upload profile picture
router.post('/profile-picture',
  authenticateToken,
  uploadRateLimit,
  ...uploadController.uploadProfilePicture
);

// Upload grievance attachments
router.post('/grievance-attachments',
  authenticateToken,
  uploadRateLimit,
  ...uploadController.uploadGrievanceAttachments
);

// General file upload
router.post('/general',
  authenticateToken,
  uploadRateLimit,
  ...uploadController.uploadGeneral
);

// Get file info
router.get('/info/:filename',
  authenticateToken,
  validateParams(filenameSchema),
  uploadController.getFileInfo
);

// Delete file
router.delete('/:filename',
  authenticateToken,
  validateParams(filenameSchema),
  uploadController.deleteFile
);

// Get user's documents
router.get('/my-documents',
  authenticateToken,
  uploadController.getMyDocuments
);

// Download specific document
router.get('/download/:documentId',
  authenticateToken,
  uploadController.downloadDocument
);

// Clean up old files (admin only)
router.delete('/cleanup/old-files',
  authenticateToken,
  requireAdmin,
  validateQuery(cleanupQuerySchema),
  uploadController.cleanupOldFiles
);

export default router;