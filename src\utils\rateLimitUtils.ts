/**
 * Simplified Rate Limiting Bypass Utility
 * Much simpler utility for managing the rateLimitDisabled flag in localStorage
 */

export const rateLimitBypass = {
  /**
   * Check if rate limiting is disabled
   */
  isEnabled(): boolean {
    return localStorage.getItem("rateLimitDisabled") === "true";
  },

  /**
   * Enable rate limiting bypass
   */
  enable(): void {
    localStorage.setItem("rateLimitDisabled", "true");
    console.log("🚫 Rate limiting disabled for development");
  },

  /**
   * Disable rate limiting bypass (enable rate limiting)
   */
  disable(): void {
    localStorage.setItem("rateLimitDisabled", "false");
    console.log("✅ Rate limiting enabled");
  },

  /**
   * Toggle rate limiting bypass state
   */
  toggle(): boolean {
    const newState = !this.isEnabled();
    if (newState) {
      this.enable();
    } else {
      this.disable();
    }
    return newState;
  },

  /**
   * Log current status
   */
  logStatus(): void {
    const status = this.isEnabled() ? "DISABLED" : "ENABLED";
    console.log(`🔄 Rate limiting status: ${status}`);
  },
};

// Legacy export for backward compatibility
export const rateLimitUtils = {
  isDisabled: rateLimitBypass.isEnabled,
  disable: rateLimitBypass.enable,
  enable: rateLimitBypass.disable,
  toggle: rateLimitBypass.toggle,
  logStatus: rateLimitBypass.logStatus,

  // Deprecated methods - no longer needed with unified API client
  addDelay: async () => {
    /* No-op - artificial delays removed */
  },
  getHeaders: (headers: Record<string, string> = {}) => headers,
  processErrorMessage: (message: string) => message,
};

export default rateLimitBypass;
