/**
 * Rate Limiting Utilities
 * Provides consistent handling of rate limiting bypass functionality
 */

export const rateLimitUtils = {
  /**
   * Check if rate limiting is disabled
   * @returns {boolean} True if rate limiting is disabled
   */
  isDisabled(): boolean {
    return localStorage.getItem('rateLimitDisabled') === 'true';
  },

  /**
   * Enable rate limiting bypass
   */
  disable(): void {
    localStorage.setItem('rateLimitDisabled', 'true');
    console.log('🚫 Rate limiting disabled for development');
  },

  /**
   * Disable rate limiting bypass (enable rate limiting)
   */
  enable(): void {
    localStorage.setItem('rateLimitDisabled', 'false');
    console.log('✅ Rate limiting enabled');
  },

  /**
   * Toggle rate limiting bypass state
   * @returns {boolean} New state (true = disabled, false = enabled)
   */
  toggle(): boolean {
    const newState = !this.isDisabled();
    if (newState) {
      this.disable();
    } else {
      this.enable();
    }
    return newState;
  },

  /**
   * Add delay for better UX only if rate limiting is enabled
   * @param {number} ms - Delay in milliseconds
   * @returns {Promise<void>}
   */
  async addDelay(ms: number = 500): Promise<void> {
    if (!this.isDisabled()) {
      await new Promise(resolve => setTimeout(resolve, ms));
    }
  },

  /**
   * Get headers for API requests with bypass header if needed
   * @param {Record<string, string>} existingHeaders - Existing headers
   * @returns {Record<string, string>} Headers with bypass header if needed
   */
  getHeaders(existingHeaders: Record<string, string> = {}): Record<string, string> {
    const headers = { ...existingHeaders };
    
    if (this.isDisabled()) {
      headers['X-Bypass-Rate-Limit'] = 'true';
    }
    
    return headers;
  },

  /**
   * Handle rate limit error messages
   * @param {string} errorMessage - Original error message
   * @returns {string} Processed error message
   */
  processErrorMessage(errorMessage: string): string {
    if (this.isDisabled() && 
        (errorMessage.includes('rate limit') || 
         errorMessage.includes('too many') ||
         errorMessage.includes('Too many'))) {
      return 'Request failed - please check your input and try again';
    }
    return errorMessage;
  },

  /**
   * Log rate limiting status for debugging
   */
  logStatus(): void {
    const status = this.isDisabled() ? 'DISABLED' : 'ENABLED';
    console.log(`🔄 Rate limiting status: ${status}`);
  }
};

export default rateLimitUtils;
