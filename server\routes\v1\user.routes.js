// User Routes v1
import express from 'express';
import <PERSON><PERSON> from 'joi';
import { validate, validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken } from '../../middleware/auth.middleware.js';
import { apiRateLimit } from '../../middleware/rateLimit.js';
import userController from '../../controllers/user.controller.js';

const router = express.Router();

// Validation schemas
const updateProfileSchema = Joi.object({
  fullName: Joi.string().min(2).max(50).optional(),
  languagePreference: Joi.string().valid('English', 'Spanish', 'French', 'German', 'Chinese', 'Japanese').optional(),
  notificationPreferences: Joi.object({
    statusChange: Joi.boolean().optional(),
    newGrievanceFiled: Joi.boolean().optional(),
    remarksAdded: Joi.boolean().optional()
  }).optional(),
  preferences: Joi.object({
    theme: Joi.string().optional(),
    language: Joi.string().optional(),
    notificationsEnabled: Joi.boolean().optional(),
    emailNotifications: Joi.boolean().optional(),
    smsNotifications: Joi.boolean().optional(),
    timezone: Joi.string().optional()
  }).optional()
});

const deleteAccountSchema = Joi.object({
  password: Joi.string().required()
});

const updatePreferencesSchema = Joi.object({
  theme: Joi.string().valid('dark', 'ocean', 'sunset', 'forest', 'arctic', 'volcano', 'mono', 'vibrant', 'royal', 'golden', 'cyber', 'pastel', 'mint', 'lavender', 'peach', 'sage').optional(),
  language: Joi.string().valid('en', 'es', 'fr', 'de', 'zh', 'ja').optional(),
  notificationsEnabled: Joi.boolean().optional(),
  emailNotifications: Joi.boolean().optional(),
  smsNotifications: Joi.boolean().optional(),
  timezone: Joi.string().optional()
});

const activityQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  activityType: Joi.string().optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional()
});

const statsQuerySchema = Joi.object({
  days: Joi.number().integer().min(1).max(365).default(30)
});

// Apply rate limiting to all user routes
router.use(apiRateLimit);

// Routes

// Get user profile
router.get('/profile',
  authenticateToken,
  userController.getProfile
);

// Update user profile
router.put('/profile',
  authenticateToken,
  validate(updateProfileSchema),
  userController.updateProfile
);

// Upload profile picture
router.post('/profile/upload-picture',
  authenticateToken,
  userController.uploadProfilePicture
);

// Get user documents
router.get('/documents',
  authenticateToken,
  userController.getUserDocuments
);

// Delete user account
router.delete('/delete-account',
  authenticateToken,
  validate(deleteAccountSchema),
  userController.deleteAccount
);

// Get user activity history
router.get('/activity',
  authenticateToken,
  validateQuery(activityQuerySchema),
  userController.getActivityHistory
);

// Get user statistics
router.get('/stats',
  authenticateToken,
  validateQuery(statsQuerySchema),
  userController.getUserStats
);

// Update user preferences
router.put('/preferences',
  authenticateToken,
  validate(updatePreferencesSchema),
  userController.updatePreferences
);

export default router;