{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Mon Jul 07 2025 22:36:08 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 15080,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 138108928,
      heapTotal: 93704192,
      heapUsed: 46143296,
      external: 22107638,
      arrayBuffers: 18406322
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 33099.093 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 22:36:08.124'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Mon Jul 07 2025 22:36:16 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 13020,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 136699904,
      heapTotal: 91869184,
      heapUsed: 47313160,
      external: 22107780,
      arrayBuffers: 18406464
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 33107.328 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 22:36:16.362'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Mon Jul 07 2025 22:36:37 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 17476,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js',
      'server'
    ],
    memoryUsage: {
      rss: 136298496,
      heapTotal: 91869184,
      heapUsed: 47331984,
      external: 22107764,
      arrayBuffers: 18406448
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 33128.406 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 22:36:37.440'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Mon Jul 07 2025 22:37:15 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 11636,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 138051584,
      heapTotal: 94228480,
      heapUsed: 46174744,
      external: 22107638,
      arrayBuffers: 18406322
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 33166.078 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 22:37:15.101'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Mon Jul 07 2025 22:38:09 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 6208,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 137261056,
      heapTotal: 93179904,
      heapUsed: 46150160,
      external: 22107638,
      arrayBuffers: 18406322
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 33220.937 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-07 22:38:09.964'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 00:15:37 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 18044,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 138285056,
      heapTotal: 93384704,
      heapUsed: 59104520,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 39068.812 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 00:15:37.838'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 00:15:47 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 18180,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 137641984,
      heapTotal: 93646848,
      heapUsed: 59288752,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 39078.625 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 00:15:47.652'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 01:36:26 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 6732,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 140238848,
      heapTotal: 93646848,
      heapUsed: 49263544,
      external: 22097654,
      arrayBuffers: 18396338
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 43917.828 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 01:36:26.863'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 01:44:04 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 17104,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 136933376,
      heapTotal: 92598272,
      heapUsed: 50543744,
      external: 22102480,
      arrayBuffers: 18401164
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 44375.468 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 01:44:04.499'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 01:46:31 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 13576,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js',
      'server'
    ],
    memoryUsage: {
      rss: 139141120,
      heapTotal: 92860416,
      heapUsed: 59268656,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 44522.312 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 01:46:31.340'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 01:46:50 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 10884,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 139812864,
      heapTotal: 93646848,
      heapUsed: 59106960,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 44541.875 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 01:46:50.899'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 01:47:00 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 13864,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 143872000,
      heapTotal: 98889728,
      heapUsed: 51717216,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 44551.406 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 01:47:00.433'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 03:09:14 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 3332,
    uid: null,
    gid: null,
    cwd: 'c:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'c:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 145993728,
      heapTotal: 93646848,
      heapUsed: 59267680,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 49485.843 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 03:09:14.867'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 03:09:30 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 12952,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 149954560,
      heapTotal: 98627584,
      heapUsed: 50988520,
      external: 22107780,
      arrayBuffers: 18406464
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 49501.578 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 03:09:30.610'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 03:48:31 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 14132,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 143417344,
      heapTotal: 93384704,
      heapUsed: 49429560,
      external: 22098823,
      arrayBuffers: 18397507
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 51842.484 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 03:48:31.514'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 03:54:23 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 18820,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 143548416,
      heapTotal: 93646848,
      heapUsed: 58982816,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 52194.937 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 03:54:23.958'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 03:56:04 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 19344,
    uid: null,
    gid: null,
    cwd: 'c:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'c:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 144982016,
      heapTotal: 93122560,
      heapUsed: 50119560,
      external: 22101911,
      arrayBuffers: 18400595
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 52295.625 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///c:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 03:56:04.652'
}
{
  error: Error: listen EADDRINUSE: address already in use :::3005
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)
      at process.processTicksAndRejections (node:internal/process/task_queues:105:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 3005
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::3005\n' +
    'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::3005\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:14:12)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)',
  exception: true,
  date: 'Tue Jul 08 2025 05:47:44 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 15148,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 144179200,
      heapTotal: 93384704,
      heapUsed: 59144904,
      external: 22107785,
      arrayBuffers: 18406469
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 58995.25 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 14,
      method: null,
      native: false
    },
    {
      column: 5,
      file: 'node:internal/process/task_queues',
      function: 'process.processTicksAndRejections',
      line: 105,
      method: 'processTicksAndRejections',
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 05:47:44.278'
}
{
  error: Error: listen EADDRINUSE: address already in use :::5000
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)
      at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1
      at ModuleJob.run (node:internal/modules/esm/module_job:274:25)
      at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)
      at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 5000
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::5000\n' +
    'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)\n' +
    '    at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n' +
    '    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n' +
    '    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)\n' +
    '    at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n' +
    '    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n' +
    '    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)',
  exception: true,
  date: 'Tue Jul 08 2025 06:19:05 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 9844,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 143077376,
      heapTotal: 91549696,
      heapUsed: 55301280,
      external: 22070660,
      arrayBuffers: 18369344
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 60876.765 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 12,
      method: null,
      native: false
    },
    {
      column: 1,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: null,
      line: 75,
      method: null,
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/module_job',
      function: 'ModuleJob.run',
      line: 274,
      method: 'run',
      native: false
    },
    {
      column: 26,
      file: 'node:internal/modules/esm/loader',
      function: 'async onImport.tracePromise.__proto__',
      line: 644,
      method: '__proto__',
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'async asyncRunEntryPointWithESMLoader',
      line: 117,
      method: null,
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 06:19:05.793'
}
{
  error: Error: listen EADDRINUSE: address already in use :::5000
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)
      at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1
      at ModuleJob.run (node:internal/modules/esm/module_job:274:25)
      at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)
      at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 5000
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::5000\n' +
    'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)\n' +
    '    at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n' +
    '    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n' +
    '    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)\n' +
    '    at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n' +
    '    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n' +
    '    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)',
  exception: true,
  date: 'Thu Jul 10 2025 03:54:39 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 1852,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 133988352,
      heapTotal: 92073984,
      heapUsed: 55233304,
      external: 22070660,
      arrayBuffers: 18369344
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 111869.5 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 12,
      method: null,
      native: false
    },
    {
      column: 1,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: null,
      line: 75,
      method: null,
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/module_job',
      function: 'ModuleJob.run',
      line: 274,
      method: 'run',
      native: false
    },
    {
      column: 26,
      file: 'node:internal/modules/esm/loader',
      function: 'async onImport.tracePromise.__proto__',
      line: 644,
      method: '__proto__',
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'async asyncRunEntryPointWithESMLoader',
      line: 117,
      method: null,
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-10 03:54:39.522'
}
{
  error: Error: listen EADDRINUSE: address already in use :::5000
      at Server.setupListenHandle [as _listen2] (node:net:1939:16)
      at listenInCluster (node:net:1996:12)
      at Server.listen (node:net:2101:7)
      at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)
      at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1
      at ModuleJob.run (node:internal/modules/esm/module_job:274:25)
      at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)
      at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5) {
    code: 'EADDRINUSE',
    errno: -4091,
    syscall: 'listen',
    address: '::',
    port: 5000
  },
  level: 'error',
  message: 'uncaughtException: listen EADDRINUSE: address already in use :::5000\n' +
    'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)\n' +
    '    at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n' +
    '    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n' +
    '    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)',
  stack: 'Error: listen EADDRINUSE: address already in use :::5000\n' +
    '    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n' +
    '    at listenInCluster (node:net:1996:12)\n' +
    '    at Server.listen (node:net:2101:7)\n' +
    '    at startServer (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:12:12)\n' +
    '    at file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js:75:1\n' +
    '    at ModuleJob.run (node:internal/modules/esm/module_job:274:25)\n' +
    '    at async onImport.tracePromise.__proto__ (node:internal/modules/esm/loader:644:26)\n' +
    '    at async asyncRunEntryPointWithESMLoader (node:internal/modules/run_main:117:5)',
  exception: true,
  date: 'Thu Jul 10 2025 03:54:56 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 24548,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 138993664,
      heapTotal: 97841152,
      heapUsed: 46920024,
      external: 22070655,
      arrayBuffers: 18369339
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 111886.062 },
  trace: [
    {
      column: 16,
      file: 'node:net',
      function: 'Server.setupListenHandle [as _listen2]',
      line: 1939,
      method: 'setupListenHandle [as _listen2]',
      native: false
    },
    {
      column: 12,
      file: 'node:net',
      function: 'listenInCluster',
      line: 1996,
      method: null,
      native: false
    },
    {
      column: 7,
      file: 'node:net',
      function: 'Server.listen',
      line: 2101,
      method: 'listen',
      native: false
    },
    {
      column: 12,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: 'startServer',
      line: 12,
      method: null,
      native: false
    },
    {
      column: 1,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/start.js',
      function: null,
      line: 75,
      method: null,
      native: false
    },
    {
      column: 25,
      file: 'node:internal/modules/esm/module_job',
      function: 'ModuleJob.run',
      line: 274,
      method: 'run',
      native: false
    },
    {
      column: 26,
      file: 'node:internal/modules/esm/loader',
      function: 'async onImport.tracePromise.__proto__',
      line: 644,
      method: '__proto__',
      native: false
    },
    {
      column: 5,
      file: 'node:internal/modules/run_main',
      function: 'async asyncRunEntryPointWithESMLoader',
      line: 117,
      method: null,
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-10 03:54:56.091'
}
