{
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  category: 'error',
  level: 'error',
  message: 'Test error message',
  timestamp: '2025-07-07 19:13:55.642'
}
{
  error: MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
      at Topology.selectServer (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:326:38)
      at async Topology._connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:200:28)
      at async Topology.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:152:13)
      at async topologyConnect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:246:17)
      at async MongoClient._connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:259:13)
      at async MongoClient.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:184:13)
      at async MongoClient.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:406:16)
      at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19) {
    errorLabelSet: Set(0) {},
    reason: TopologyDescription {
      type: 'Unknown',
      servers: Map(1) {
        'localhost:27017' => ServerDescription {
          address: 'localhost:27017',
          type: 'Unknown',
          hosts: [],
          passives: [],
          arbiters: [],
          tags: {},
          minWireVersion: 0,
          maxWireVersion: 0,
          roundTripTime: -1,
          minRoundTripTime: 0,
          lastUpdateTime: 73981,
          lastWriteDate: 0,
          error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
              at Socket.<anonymous> (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\cmap\connect.js:286:44)
              at Object.onceWrapper (node:events:633:26)
              at Socket.emit (node:events:518:28)
              at emitErrorNT (node:internal/streams/destroy:170:8)
              at emitErrorCloseNT (node:internal/streams/destroy:129:3)
              at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
            errorLabelSet: Set(1) { 'ResetPool' },
            beforeHandshake: false,
            [cause]: AggregateError [ECONNREFUSED]: 
                at internalConnectMultiple (node:net:1139:18)
                at afterConnectMultiple (node:net:1714:7) {
              code: 'ECONNREFUSED',
              [errors]: [
                Error: connect ECONNREFUSED ::1:27017
                    at createConnectionError (node:net:1677:14)
                    at afterConnectMultiple (node:net:1707:16) {
                  errno: -4078,
                  code: 'ECONNREFUSED',
                  syscall: 'connect',
                  address: '::1',
                  port: 27017
                },
                Error: connect ECONNREFUSED 127.0.0.1:27017
                    at createConnectionError (node:net:1677:14)
                    at afterConnectMultiple (node:net:1707:16) {
                  errno: -4078,
                  code: 'ECONNREFUSED',
                  syscall: 'connect',
                  address: '127.0.0.1',
                  port: 27017
                }
              ]
            }
          },
          topologyVersion: null,
          setName: null,
          setVersion: null,
          electionId: null,
          logicalSessionTimeoutMinutes: null,
          maxMessageSizeBytes: null,
          maxWriteBatchSize: null,
          maxBsonObjectSize: null,
          primary: null,
          me: null,
          '$clusterTime': null,
          iscryptd: false
        }
      },
      stale: false,
      compatible: true,
      heartbeatFrequencyMS: 10000,
      localThresholdMS: 15,
      setName: null,
      maxElectionId: null,
      maxSetVersion: null,
      commonWireVersion: 0,
      logicalSessionTimeoutMinutes: null
    },
    code: undefined,
    [cause]: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
        at Socket.<anonymous> (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\cmap\connect.js:286:44)
        at Object.onceWrapper (node:events:633:26)
        at Socket.emit (node:events:518:28)
        at emitErrorNT (node:internal/streams/destroy:170:8)
        at emitErrorCloseNT (node:internal/streams/destroy:129:3)
        at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
      errorLabelSet: Set(1) { 'ResetPool' },
      beforeHandshake: false,
      [cause]: AggregateError [ECONNREFUSED]: 
          at internalConnectMultiple (node:net:1139:18)
          at afterConnectMultiple (node:net:1714:7) {
        code: 'ECONNREFUSED',
        [errors]: [
          Error: connect ECONNREFUSED ::1:27017
              at createConnectionError (node:net:1677:14)
              at afterConnectMultiple (node:net:1707:16) {
            errno: -4078,
            code: 'ECONNREFUSED',
            syscall: 'connect',
            address: '::1',
            port: 27017
          },
          Error: connect ECONNREFUSED 127.0.0.1:27017
              at createConnectionError (node:net:1677:14)
              at afterConnectMultiple (node:net:1707:16) {
            errno: -4078,
            code: 'ECONNREFUSED',
            syscall: 'connect',
            address: '127.0.0.1',
            port: 27017
          }
        ]
      }
    }
  },
  level: 'error',
  message: 'unhandledRejection: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    'MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at Topology.selectServer (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n' +
    '    at async Topology._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n' +
    '    at async Topology.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n' +
    '    at async topologyConnect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n' +
    '    at async MongoClient._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:406:16)\n' +
    '    at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19)',
  stack: 'MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at Topology.selectServer (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n' +
    '    at async Topology._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n' +
    '    at async Topology.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n' +
    '    at async topologyConnect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n' +
    '    at async MongoClient._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:406:16)\n' +
    '    at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19)',
  rejection: true,
  date: 'Tue Jul 08 2025 20:51:24 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 13696,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 132816896,
      heapTotal: 92860416,
      heapUsed: 57907096,
      external: 22073104,
      arrayBuffers: 18371788
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 74.328 },
  trace: [
    {
      column: 38,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'Topology.selectServer',
      line: 326,
      method: 'selectServer',
      native: false
    },
    {
      column: 28,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'async Topology._connect',
      line: 200,
      method: '_connect',
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'async Topology.connect',
      line: 152,
      method: 'connect',
      native: false
    },
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async topologyConnect',
      line: 246,
      method: null,
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient._connect',
      line: 259,
      method: '_connect',
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient.connect',
      line: 184,
      method: 'connect',
      native: false
    },
    {
      column: 16,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient.connect',
      line: 406,
      method: 'connect',
      native: false
    },
    {
      column: 19,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js',
      function: 'async connectDB',
      line: 12,
      method: null,
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 20:51:24.360'
}
{
  error: MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
      at Topology.selectServer (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:326:38)
      at async Topology._connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:200:28)
      at async Topology.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:152:13)
      at async topologyConnect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:246:17)
      at async MongoClient._connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:259:13)
      at async MongoClient.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:184:13)
      at async MongoClient.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:406:16)
      at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19) {
    errorLabelSet: Set(0) {},
    reason: TopologyDescription {
      type: 'Unknown',
      servers: Map(1) {
        'localhost:27017' => ServerDescription {
          address: 'localhost:27017',
          type: 'Unknown',
          hosts: [],
          passives: [],
          arbiters: [],
          tags: {},
          minWireVersion: 0,
          maxWireVersion: 0,
          roundTripTime: -1,
          minRoundTripTime: 0,
          lastUpdateTime: 3188530,
          lastWriteDate: 0,
          error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
              at Socket.<anonymous> (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\cmap\connect.js:286:44)
              at Object.onceWrapper (node:events:633:26)
              at Socket.emit (node:events:518:28)
              at emitErrorNT (node:internal/streams/destroy:170:8)
              at emitErrorCloseNT (node:internal/streams/destroy:129:3)
              at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
            errorLabelSet: Set(1) { 'ResetPool' },
            beforeHandshake: false,
            [cause]: AggregateError [ECONNREFUSED]: 
                at internalConnectMultiple (node:net:1139:18)
                at afterConnectMultiple (node:net:1714:7) {
              code: 'ECONNREFUSED',
              [errors]: [
                Error: connect ECONNREFUSED ::1:27017
                    at createConnectionError (node:net:1677:14)
                    at afterConnectMultiple (node:net:1707:16) {
                  errno: -4078,
                  code: 'ECONNREFUSED',
                  syscall: 'connect',
                  address: '::1',
                  port: 27017
                },
                Error: connect ECONNREFUSED 127.0.0.1:27017
                    at createConnectionError (node:net:1677:14)
                    at afterConnectMultiple (node:net:1707:16) {
                  errno: -4078,
                  code: 'ECONNREFUSED',
                  syscall: 'connect',
                  address: '127.0.0.1',
                  port: 27017
                }
              ]
            }
          },
          topologyVersion: null,
          setName: null,
          setVersion: null,
          electionId: null,
          logicalSessionTimeoutMinutes: null,
          maxMessageSizeBytes: null,
          maxWriteBatchSize: null,
          maxBsonObjectSize: null,
          primary: null,
          me: null,
          '$clusterTime': null,
          iscryptd: false
        }
      },
      stale: false,
      compatible: true,
      heartbeatFrequencyMS: 10000,
      localThresholdMS: 15,
      setName: null,
      maxElectionId: null,
      maxSetVersion: null,
      commonWireVersion: 0,
      logicalSessionTimeoutMinutes: null
    },
    code: undefined,
    [cause]: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
        at Socket.<anonymous> (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\cmap\connect.js:286:44)
        at Object.onceWrapper (node:events:633:26)
        at Socket.emit (node:events:518:28)
        at emitErrorNT (node:internal/streams/destroy:170:8)
        at emitErrorCloseNT (node:internal/streams/destroy:129:3)
        at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
      errorLabelSet: Set(1) { 'ResetPool' },
      beforeHandshake: false,
      [cause]: AggregateError [ECONNREFUSED]: 
          at internalConnectMultiple (node:net:1139:18)
          at afterConnectMultiple (node:net:1714:7) {
        code: 'ECONNREFUSED',
        [errors]: [
          Error: connect ECONNREFUSED ::1:27017
              at createConnectionError (node:net:1677:14)
              at afterConnectMultiple (node:net:1707:16) {
            errno: -4078,
            code: 'ECONNREFUSED',
            syscall: 'connect',
            address: '::1',
            port: 27017
          },
          Error: connect ECONNREFUSED 127.0.0.1:27017
              at createConnectionError (node:net:1677:14)
              at afterConnectMultiple (node:net:1707:16) {
            errno: -4078,
            code: 'ECONNREFUSED',
            syscall: 'connect',
            address: '127.0.0.1',
            port: 27017
          }
        ]
      }
    }
  },
  level: 'error',
  message: 'unhandledRejection: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    'MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at Topology.selectServer (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n' +
    '    at async Topology._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n' +
    '    at async Topology.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n' +
    '    at async topologyConnect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n' +
    '    at async MongoClient._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:406:16)\n' +
    '    at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19)',
  stack: 'MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at Topology.selectServer (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n' +
    '    at async Topology._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n' +
    '    at async Topology.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n' +
    '    at async topologyConnect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n' +
    '    at async MongoClient._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:406:16)\n' +
    '    at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19)',
  rejection: true,
  date: 'Tue Jul 08 2025 21:43:18 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 12376,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 139694080,
      heapTotal: 98365440,
      heapUsed: 49056616,
      external: 22073099,
      arrayBuffers: 18371783
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 3188.89 },
  trace: [
    {
      column: 38,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'Topology.selectServer',
      line: 326,
      method: 'selectServer',
      native: false
    },
    {
      column: 28,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'async Topology._connect',
      line: 200,
      method: '_connect',
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'async Topology.connect',
      line: 152,
      method: 'connect',
      native: false
    },
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async topologyConnect',
      line: 246,
      method: null,
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient._connect',
      line: 259,
      method: '_connect',
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient.connect',
      line: 184,
      method: 'connect',
      native: false
    },
    {
      column: 16,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient.connect',
      line: 406,
      method: 'connect',
      native: false
    },
    {
      column: 19,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js',
      function: 'async connectDB',
      line: 12,
      method: null,
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 21:43:18.920'
}
{
  error: MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
      at Topology.selectServer (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:326:38)
      at async Topology._connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:200:28)
      at async Topology.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\sdam\topology.js:152:13)
      at async topologyConnect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:246:17)
      at async MongoClient._connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:259:13)
      at async MongoClient.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:184:13)
      at async MongoClient.connect (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\mongo_client.js:406:16)
      at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19) {
    errorLabelSet: Set(0) {},
    reason: TopologyDescription {
      type: 'Unknown',
      servers: Map(1) {
        'localhost:27017' => ServerDescription {
          address: 'localhost:27017',
          type: 'Unknown',
          hosts: [],
          passives: [],
          arbiters: [],
          tags: {},
          minWireVersion: 0,
          maxWireVersion: 0,
          roundTripTime: -1,
          minRoundTripTime: 0,
          lastUpdateTime: 3235449,
          lastWriteDate: 0,
          error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
              at Socket.<anonymous> (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\cmap\connect.js:286:44)
              at Object.onceWrapper (node:events:633:26)
              at Socket.emit (node:events:518:28)
              at emitErrorNT (node:internal/streams/destroy:170:8)
              at emitErrorCloseNT (node:internal/streams/destroy:129:3)
              at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
            errorLabelSet: Set(1) { 'ResetPool' },
            beforeHandshake: false,
            [cause]: AggregateError [ECONNREFUSED]: 
                at internalConnectMultiple (node:net:1139:18)
                at afterConnectMultiple (node:net:1714:7) {
              code: 'ECONNREFUSED',
              [errors]: [
                Error: connect ECONNREFUSED ::1:27017
                    at createConnectionError (node:net:1677:14)
                    at afterConnectMultiple (node:net:1707:16) {
                  errno: -4078,
                  code: 'ECONNREFUSED',
                  syscall: 'connect',
                  address: '::1',
                  port: 27017
                },
                Error: connect ECONNREFUSED 127.0.0.1:27017
                    at createConnectionError (node:net:1677:14)
                    at afterConnectMultiple (node:net:1707:16) {
                  errno: -4078,
                  code: 'ECONNREFUSED',
                  syscall: 'connect',
                  address: '127.0.0.1',
                  port: 27017
                }
              ]
            }
          },
          topologyVersion: null,
          setName: null,
          setVersion: null,
          electionId: null,
          logicalSessionTimeoutMinutes: null,
          maxMessageSizeBytes: null,
          maxWriteBatchSize: null,
          maxBsonObjectSize: null,
          primary: null,
          me: null,
          '$clusterTime': null,
          iscryptd: false
        }
      },
      stale: false,
      compatible: true,
      heartbeatFrequencyMS: 10000,
      localThresholdMS: 15,
      setName: null,
      maxElectionId: null,
      maxSetVersion: null,
      commonWireVersion: 0,
      logicalSessionTimeoutMinutes: null
    },
    code: undefined,
    [cause]: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
        at Socket.<anonymous> (C:\Users\<USER>\Downloads\AI Projects\Project One\node_modules\mongodb\lib\cmap\connect.js:286:44)
        at Object.onceWrapper (node:events:633:26)
        at Socket.emit (node:events:518:28)
        at emitErrorNT (node:internal/streams/destroy:170:8)
        at emitErrorCloseNT (node:internal/streams/destroy:129:3)
        at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
      errorLabelSet: Set(1) { 'ResetPool' },
      beforeHandshake: false,
      [cause]: AggregateError [ECONNREFUSED]: 
          at internalConnectMultiple (node:net:1139:18)
          at afterConnectMultiple (node:net:1714:7) {
        code: 'ECONNREFUSED',
        [errors]: [
          Error: connect ECONNREFUSED ::1:27017
              at createConnectionError (node:net:1677:14)
              at afterConnectMultiple (node:net:1707:16) {
            errno: -4078,
            code: 'ECONNREFUSED',
            syscall: 'connect',
            address: '::1',
            port: 27017
          },
          Error: connect ECONNREFUSED 127.0.0.1:27017
              at createConnectionError (node:net:1677:14)
              at afterConnectMultiple (node:net:1707:16) {
            errno: -4078,
            code: 'ECONNREFUSED',
            syscall: 'connect',
            address: '127.0.0.1',
            port: 27017
          }
        ]
      }
    }
  },
  level: 'error',
  message: 'unhandledRejection: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    'MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at Topology.selectServer (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n' +
    '    at async Topology._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n' +
    '    at async Topology.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n' +
    '    at async topologyConnect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n' +
    '    at async MongoClient._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:406:16)\n' +
    '    at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19)',
  stack: 'MongoServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at Topology.selectServer (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:326:38)\n' +
    '    at async Topology._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:200:28)\n' +
    '    at async Topology.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js:152:13)\n' +
    '    at async topologyConnect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:246:17)\n' +
    '    at async MongoClient._connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:259:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:184:13)\n' +
    '    at async MongoClient.connect (C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js:406:16)\n' +
    '    at async connectDB (file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js:12:19)',
  rejection: true,
  date: 'Tue Jul 08 2025 21:44:05 GMT-0700 (Pacific Daylight Time)',
  process: {
    pid: 14580,
    uid: null,
    gid: null,
    cwd: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One',
    execPath: 'C:\\Program Files\\nodejs\\node.exe',
    version: 'v22.16.0',
    argv: [
      'C:\\Program Files\\nodejs\\node.exe',
      'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\server\\start.js'
    ],
    memoryUsage: {
      rss: 134197248,
      heapTotal: 92598272,
      heapUsed: 58242496,
      external: 22073104,
      arrayBuffers: 18371788
    }
  },
  os: { loadavg: [ 0, 0, 0 ], uptime: 3235.734 },
  trace: [
    {
      column: 38,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'Topology.selectServer',
      line: 326,
      method: 'selectServer',
      native: false
    },
    {
      column: 28,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'async Topology._connect',
      line: 200,
      method: '_connect',
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\sdam\\topology.js',
      function: 'async Topology.connect',
      line: 152,
      method: 'connect',
      native: false
    },
    {
      column: 17,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async topologyConnect',
      line: 246,
      method: null,
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient._connect',
      line: 259,
      method: '_connect',
      native: false
    },
    {
      column: 13,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient.connect',
      line: 184,
      method: 'connect',
      native: false
    },
    {
      column: 16,
      file: 'C:\\Users\\<USER>\\Downloads\\AI Projects\\Project One\\node_modules\\mongodb\\lib\\mongo_client.js',
      function: 'async MongoClient.connect',
      line: 406,
      method: 'connect',
      native: false
    },
    {
      column: 19,
      file: 'file:///C:/Users/<USER>/Downloads/AI%20Projects/Project%20One/server/config/database.js',
      function: 'async connectDB',
      line: 12,
      method: null,
      native: false
    }
  ],
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  timestamp: '2025-07-08 21:44:05.753'
}
{
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  userId: new ObjectId('6865571886471cc90b84fc7f'),
  error: 'Email service not configured',
  category: 'error',
  level: 'error',
  message: 'Failed to send password reset email',
  timestamp: '2025-07-10 03:22:02.878'
}
{
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  userId: new ObjectId('6865571886471cc90b84fc7f'),
  error: 'Email service not configured',
  category: 'error',
  level: 'error',
  message: 'Failed to send password reset email',
  timestamp: '2025-07-10 03:23:03.518'
}
{
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  userId: new ObjectId('6865571886471cc90b84fc7f'),
  error: 'Email service not configured',
  category: 'error',
  level: 'error',
  message: 'Failed to send password reset email',
  timestamp: '2025-07-10 04:03:22.935'
}
{
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  userId: new ObjectId('6865571886471cc90b84fc7f'),
  error: 'Email service not configured',
  category: 'error',
  level: 'error',
  message: 'Failed to send password reset email',
  timestamp: '2025-07-10 04:15:47.890'
}
{
  service: 'civicassist-backend',
  environment: 'development',
  version: '1.0.0',
  userId: new ObjectId('6865571886471cc90b84fc7f'),
  error: 'Email service not configured',
  category: 'error',
  level: 'error',
  message: 'Failed to send password reset email',
  timestamp: '2025-07-10 04:19:15.789'
}
