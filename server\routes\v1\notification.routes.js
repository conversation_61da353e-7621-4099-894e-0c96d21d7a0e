// Notification Routes v1
import express from 'express';
import Jo<PERSON> from 'joi';
import { validate, validatePara<PERSON>, validateQuery } from '../../middleware/validation.middleware.js';
import { authenticateToken, requireAdmin } from '../../middleware/auth.middleware.js';
import { apiRateLimit } from '../../middleware/rateLimit.js';
import notificationController from '../../controllers/notification.controller.js';

const router = express.Router();

// Validation schemas
const notificationQuerySchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  unreadOnly: Joi.boolean().default(false),
  type: Joi.string().valid(
    'grievance_status_update', 'new_remark', 'system_alert', 
    'chat_response', 'report_generated', 'system_announcement'
  ).optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional()
});

const objectIdSchema = Joi.object({
  id: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required()
});

const createNotificationSchema = Joi.object({
  userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
  type: Joi.string().valid(
    'grievance_status_update', 'new_remark', 'system_alert', 
    'chat_response', 'report_generated', 'system_announcement'
  ).default('system_announcement'),
  title: Joi.string().min(1).max(100).required(),
  message: Joi.string().min(1).max(500).required(),
  priority: Joi.string().valid('low', 'medium', 'high').default('medium'),
  channels: Joi.array().items(Joi.string().valid('web', 'email', 'sms')).default(['web'])
});

const bulkNotificationSchema = Joi.object({
  notifications: Joi.array().items(Joi.object({
    userId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/).required(),
    type: Joi.string().optional(),
    title: Joi.string().min(1).max(100).required(),
    message: Joi.string().min(1).max(500).required(),
    priority: Joi.string().valid('low', 'medium', 'high').optional(),
    channels: Joi.array().items(Joi.string().valid('web', 'email', 'sms')).optional()
  })).min(1).required()
});

const cleanupQuerySchema = Joi.object({
  daysOld: Joi.number().integer().min(1).max(365).default(30)
});

const notificationPreferencesSchema = Joi.object({
  preferences: Joi.object({
    inApp: Joi.object({
      enabled: Joi.boolean().required(),
      types: Joi.array().items(Joi.string()).required()
    }).required(),
    email: Joi.object({
      enabled: Joi.boolean().required(),
      types: Joi.array().items(Joi.string()).required()
    }).required(),
    sms: Joi.object({
      enabled: Joi.boolean().required(),
      types: Joi.array().items(Joi.string()).required()
    }).required()
  }).required()
});

const templateSchema = Joi.object({
  type: Joi.string().required(),
  subjectTemplate: Joi.string().required(),
  bodyTemplate: Joi.string().required(),
  channels: Joi.array().items(Joi.string().valid('in-app', 'email', 'sms')).default(['in-app']),
  variables: Joi.array().items(Joi.string()).default([])
});

// Apply rate limiting to all notification routes
router.use(apiRateLimit);

// Routes

// Get user notifications
router.get('/',
  authenticateToken,
  validateQuery(notificationQuerySchema),
  notificationController.getNotifications
);

// Get unread count
router.get('/unread-count',
  authenticateToken,
  notificationController.getUnreadCount
);

// Get notification statistics
router.get('/stats',
  authenticateToken,
  notificationController.getNotificationStats
);

// Mark notification as read
router.put('/:id/read',
  authenticateToken,
  validateParams(objectIdSchema),
  notificationController.markAsRead
);

// Mark all notifications as read
router.put('/mark-all-read',
  authenticateToken,
  notificationController.markAllAsRead
);

// Delete notification
router.delete('/:id',
  authenticateToken,
  validateParams(objectIdSchema),
  notificationController.deleteNotification
);

// Create notification (admin only)
router.post('/',
  authenticateToken,
  requireAdmin,
  validate(createNotificationSchema),
  notificationController.createNotification
);

// Create bulk notifications (admin only)
router.post('/bulk',
  authenticateToken,
  requireAdmin,
  validate(bulkNotificationSchema),
  notificationController.createBulkNotifications
);

// Clean up old notifications (admin only)
router.delete('/cleanup',
  authenticateToken,
  requireAdmin,
  validateQuery(cleanupQuerySchema),
  notificationController.cleanupOldNotifications
);

// Notification preferences
router.get('/preferences',
  authenticateToken,
  notificationController.getNotificationPreferences
);

router.put('/preferences',
  authenticateToken,
  validate(notificationPreferencesSchema),
  notificationController.updateNotificationPreferences
);

// Template management (admin only)
router.post('/templates',
  authenticateToken,
  requireAdmin,
  validate(templateSchema),
  notificationController.createNotificationTemplate
);

router.get('/templates',
  authenticateToken,
  requireAdmin,
  notificationController.getNotificationTemplates
);

router.put('/templates/:id',
  authenticateToken,
  requireAdmin,
  validateParams(objectIdSchema),
  validate(templateSchema),
  notificationController.updateNotificationTemplate
);

export default router;