// Authentication Routes v1
import express from 'express';
import <PERSON><PERSON> from 'joi';
import { validate } from '../../middleware/validation.middleware.js';
import { authenticateToken } from '../../middleware/auth.middleware.js';
import { authRateLimit, passwordResetRateLimit, loginRateLimit, registrationRateLimit } from '../../middleware/rateLimit.js';
import authController from '../../controllers/auth.controller.js';

const router = express.Router();

// Validation schemas
const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required()
});

const registerSchema = Joi.object({
  name: Joi.string().min(2).max(50).required(),
  email: Joi.string().email().required(),
  password: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    }),
  mobile: Joi.string().optional(),
  address: Joi.string().optional()
});

const forgotPasswordSchema = Joi.object({
  email: Joi.string().email().required()
});

const resetPasswordSchema = Joi.object({
  token: Joi.string().required(),
  newPassword: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
    .messages({
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    })
});

const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string()
    .min(8)
    .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .required()
});

const switchToAdminSchema = Joi.object({
  password: Joi.string().required()
});

const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required()
});

// Routes
router.post('/login', 
  loginRateLimit,
  validate(loginSchema),
  authController.login
);

router.post('/register',
  registrationRateLimit,
  validate(registerSchema),
  authController.register
);

router.post('/forgot-password',
  passwordResetRateLimit,
  validate(forgotPasswordSchema),
  authController.forgotPassword
);

router.get('/verify-reset-token',
  authController.verifyResetToken
);

router.post('/reset-password',
  authRateLimit,
  validate(resetPasswordSchema),
  authController.resetPassword
);

router.put('/change-password',
  authenticateToken,
  validate(changePasswordSchema),
  authController.changePassword
);

router.post('/switch-to-admin',
  authenticateToken,
  validate(switchToAdminSchema),
  authController.switchToAdmin
);

router.post('/refresh-token',
  validate(refreshTokenSchema),
  authController.refreshToken
);

router.post('/logout',
  authenticateToken,
  authController.logout
);

router.post('/logout-all',
  authenticateToken,
  authController.logoutAll
);

router.get('/sessions',
  authenticateToken,
  authController.getActiveSessions
);

router.get('/security-events',
  authenticateToken,
  authController.getSecurityEvents
);

router.get('/check-email',
  authController.checkEmailExists
);

export default router;