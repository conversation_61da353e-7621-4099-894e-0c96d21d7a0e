// Enhanced Chat Controller with Ollama Integration
import Chat from '../models/Chat.js';
import Grievance from '../models/Grievance.js';
import UserActivity from '../models/UserActivity.js';
import Notification from '../models/Notification.js';
import { AppError, asyncHandler } from '../middleware/error.middleware.js';
import { logger } from '../middleware/logging.middleware.js';
import { ollamaService } from '../services/ollamaService.js';

// Start a new conversation with AI
const startConversation = asyncHandler(async (req, res) => {
  const { initialMessage } = req.body;
  const userId = req.user.userId;

  if (!initialMessage || initialMessage.trim().length === 0) {
    throw new AppError('Initial message is required', 400, 'INITIAL_MESSAGE_REQUIRED');
  }

  // Validate input
  const sanitizedMessage = ollamaService.validateInput(initialMessage);

  // Create new conversation
  const conversation = await Chat.create({
    userId,
    messages: [{
      role: 'user',
      content: sanitizedMessage,
      timestamp: new Date()
    }],
    isActive: true
  });

  // Generate AI response
  try {
    const aiResponse = await ollamaService.generateResponse(sanitizedMessage, []);
    
    // Add AI response to conversation
    conversation.messages.push({
      role: 'ai',
      content: aiResponse.response,
      timestamp: new Date(),
      metadata: {
        model: aiResponse.model,
        tokenCount: aiResponse.evalCount,
        processingTime: aiResponse.totalDuration
      }
    });
    
    conversation.lastMessageAt = new Date();
    await conversation.save();
  } catch (error) {
    logger.error('AI response generation failed', {
      error: error.message,
      conversationId: conversation._id
    });
    
    // Add fallback response
    conversation.messages.push({
      role: 'ai',
      content: 'I apologize, but I\'m having trouble connecting to the AI service right now. Please try again in a moment.',
      timestamp: new Date(),
      isError: true
    });
    await conversation.save();
  }

  // Log activity
  await UserActivity.create({
    userId,
    activityType: 'chat_started',
    details: { conversationId: conversation._id },
    ipAddress: req.ip
  });

  logger.info('New chat conversation started', {
    userId,
    conversationId: conversation._id
  });

  res.status(201).json({
    success: true,
    conversationId: conversation._id,
    initialMessage: conversation.messages[0],
    aiResponse: conversation.messages[1] || null
  });
});

// Send message in conversation with AI response
const sendMessage = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const { content, attachments } = req.body;
  const userId = req.user.userId;

  // Find conversation
  const conversation = await Chat.findById(conversationId);
  if (!conversation) {
    throw new AppError('Conversation not found', 404, 'CONVERSATION_NOT_FOUND');
  }

  // Verify ownership
  if (conversation.userId.toString() !== userId) {
    throw new AppError('Access denied', 403, 'ACCESS_DENIED');
  }

  // Validate and sanitize input
  const sanitizedContent = ollamaService.validateInput(content);

  // Add user message
  const userMessage = {
    role: 'user',
    content: sanitizedContent,
    attachments: attachments || [],
    timestamp: new Date()
  };

  conversation.messages.push(userMessage);

  // Analyze user intent for special commands
  const intent = ollamaService.analyzeIntent(sanitizedContent);
  
  // Handle special commands
  if (intent.type === 'report_generation' && intent.grievanceId) {
    await handleReportGeneration(conversation, intent.grievanceId, userId, req, res);
    return;
  }

  // Generate AI response with context
  try {
    const context = conversation.messages.slice(-10); // Last 10 messages for context
    const aiResponse = await ollamaService.generateResponse(sanitizedContent, context);
    
    // Add AI response to conversation
    const aiMessage = {
      role: 'ai',
      content: aiResponse.response,
      timestamp: new Date(),
      metadata: {
        model: aiResponse.model,
        tokenCount: aiResponse.evalCount,
        processingTime: aiResponse.totalDuration,
        intent: intent.type
      }
    };
    
    conversation.messages.push(aiMessage);
    conversation.lastMessageAt = new Date();
    await conversation.save();

    // Log activity
    await UserActivity.create({
      userId,
      activityType: 'chat_message_sent',
      details: {
        conversationId,
        messageLength: content.length,
        hasAttachments: !!(attachments && attachments.length > 0),
        intent: intent.type
      },
      ipAddress: req.ip
    });

    res.json({
      success: true,
      userMessage,
      aiResponse: aiMessage
    });

  } catch (error) {
    logger.error('AI response generation failed', {
      error: error.message,
      conversationId,
      userId
    });
    
    // Add fallback response
    const fallbackMessage = {
      role: 'ai',
      content: 'I apologize, but I\'m having trouble processing your request right now. Please try again in a moment.',
      timestamp: new Date(),
      isError: true
    };
    
    conversation.messages.push(fallbackMessage);
    await conversation.save();

    res.json({
      success: true,
      userMessage,
      aiResponse: fallbackMessage
    });
  }
});

// Stream AI response via Server-Sent Events (HTTP streaming)
const streamResponse = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const { message } = req.query;
  const userId = req.user.userId;

  // Find conversation
  const conversation = await Chat.findById(conversationId);
  if (!conversation) {
    throw new AppError('Conversation not found', 404, 'CONVERSATION_NOT_FOUND');
  }

  // Verify ownership
  if (conversation.userId.toString() !== userId) {
    throw new AppError('Access denied', 403, 'ACCESS_DENIED');
  }

  // Rate limiting is now handled by unified middleware

  // Set up Server-Sent Events
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': req.headers.origin || '*',
    'Access-Control-Allow-Headers': 'Cache-Control, Authorization',
    'Access-Control-Allow-Credentials': 'true'
  });

  // Send initial connection confirmation
  res.write(`data: ${JSON.stringify({
    type: 'connected',
    conversationId,
    timestamp: new Date().toISOString()
  })}\n\n`);

  try {
    const sanitizedMessage = ollamaService.validateInput(message);
    const context = conversation.messages.slice(-10);
    
    // Add user message to conversation
    const userMessage = {
      role: 'user',
      content: sanitizedMessage,
      timestamp: new Date()
    };
    conversation.messages.push(userMessage);
    
    let fullResponse = '';
    let tokenCount = 0;
    const startTime = Date.now();
    
    // Stream AI response
    for await (const chunk of ollamaService.streamResponse(sanitizedMessage, context)) {
      if (chunk.type === 'chunk' && chunk.content) {
        fullResponse += chunk.content;
        tokenCount++;
        
        res.write(`data: ${JSON.stringify({
          type: 'chunk',
          content: chunk.content,
          fullContent: fullResponse,
          tokenCount,
          timestamp: new Date().toISOString()
        })}\n\n`);
        
        // Add small delay to prevent overwhelming the client
        await new Promise(resolve => setTimeout(resolve, 10));
        
      } else if (chunk.type === 'complete') {
        const duration = Date.now() - startTime;
        
        // Save complete response to database
        const aiMessage = {
          role: 'ai',
          content: fullResponse,
          timestamp: new Date(),
          metadata: {
            model: chunk.model,
            totalDuration: chunk.totalDuration,
            tokenCount: chunk.evalCount || tokenCount,
            streamingDuration: duration
          }
        };
        
        conversation.messages.push(aiMessage);
        conversation.lastMessageAt = new Date();
        await conversation.save();
        
        // Log activity
        await UserActivity.create({
          userId,
          activityType: 'chat_stream_completed',
          details: {
            conversationId,
            messageLength: sanitizedMessage.length,
            responseLength: fullResponse.length,
            duration,
            tokenCount
          },
          ipAddress: req.ip
        });
        
        res.write(`data: ${JSON.stringify({
          type: 'complete',
          content: fullResponse,
          metadata: {
            tokenCount: chunk.evalCount || tokenCount,
            processingTime: chunk.totalDuration,
            streamingDuration: duration,
            model: chunk.model
          },
          timestamp: new Date().toISOString()
        })}\n\n`);
        
        logger.info('Chat streaming completed', {
          userId,
          conversationId,
          duration,
          tokenCount,
          responseLength: fullResponse.length
        });
        
        break;
        
      } else if (chunk.type === 'error') {
        res.write(`data: ${JSON.stringify({
          type: 'error',
          error: chunk.error,
          timestamp: new Date().toISOString()
        })}\n\n`);
        break;
      }
    }
  } catch (error) {
    logger.error('Streaming response failed', {
      error: error.message,
      conversationId,
      userId,
      stack: error.stack
    });
    
    res.write(`data: ${JSON.stringify({
      type: 'error',
      error: 'Failed to generate response. Please try again.',
      timestamp: new Date().toISOString()
    })}\n\n`);
  } finally {
    res.end();
  }
});

// WebSocket-based streaming (for real-time chat via WebSocket)
const streamViaWebSocket = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const { message } = req.body;
  const userId = req.user.userId;

  // Find conversation
  const conversation = await Chat.findById(conversationId);
  if (!conversation) {
    throw new AppError('Conversation not found', 404, 'CONVERSATION_NOT_FOUND');
  }

  // Verify ownership
  if (conversation.userId.toString() !== userId) {
    throw new AppError('Access denied', 403, 'ACCESS_DENIED');
  }

  try {
    // Rate limiting is now handled by unified middleware

    // Validate input
    const sanitizedMessage = ollamaService.validateInput(message);
    
    // Add user message to conversation
    const userMessage = {
      role: 'user',
      content: sanitizedMessage,
      timestamp: new Date()
    };
    conversation.messages.push(userMessage);
    await conversation.save();
    
    // Get WebSocket manager and emit streaming start
    const WebSocketManager = (await import('../websocket.js')).default;
    
    // Trigger WebSocket streaming (handled in websocket.js)
    // This will be processed by the WebSocket event handlers
    
    res.json({
      success: true,
      message: 'Streaming initiated via WebSocket',
      conversationId,
      userMessage
    });
    
  } catch (error) {
    logger.error('WebSocket streaming initiation failed', {
      error: error.message,
      conversationId,
      userId
    });
    
    if (error.message.includes('Rate limit')) {
      throw new AppError(error.message, 429, 'RATE_LIMIT_EXCEEDED');
    }
    
    throw new AppError('Failed to initiate streaming', 500, 'STREAMING_FAILED');
  }
});

// Handle report generation via chat
const handleReportGeneration = async (conversation, grievanceId, userId, req, res) => {
  try {
    // Find the grievance
    const grievance = await Grievance.findOne({
      $or: [
        { _id: grievanceId },
        { referenceId: grievanceId }
      ]
    });

    if (!grievance) {
      const errorMessage = {
        role: 'ai',
        content: `I couldn't find a grievance with ID "${grievanceId}". Please check the ID and try again.`,
        timestamp: new Date()
      };
      
      conversation.messages.push(errorMessage);
      await conversation.save();
      
      return res.json({
        success: true,
        userMessage: conversation.messages[conversation.messages.length - 2],
        aiResponse: errorMessage
      });
    }

    // Check access permissions
    if (grievance.userId.toString() !== userId && !req.user.isAdmin) {
      const errorMessage = {
        role: 'ai',
        content: 'You can only generate reports for your own grievances.',
        timestamp: new Date()
      };
      
      conversation.messages.push(errorMessage);
      await conversation.save();
      
      return res.json({
        success: true,
        userMessage: conversation.messages[conversation.messages.length - 2],
        aiResponse: errorMessage
      });
    }

    // Generate AI summary of the grievance
    const grievanceContext = `Grievance Details:
Title: ${grievance.title}
Description: ${grievance.description}
Category: ${grievance.category}
Status: ${grievance.status}
Priority: ${grievance.priority}
Submitted: ${grievance.submittedAt.toLocaleDateString()}`;
    
    const summaryPrompt = `Please provide a professional summary of this grievance for a report:\n\n${grievanceContext}`;
    const aiSummary = await ollamaService.generateResponse(summaryPrompt, []);

    const responseMessage = {
      role: 'ai',
      content: `I've found your grievance "${grievance.title}" (ID: ${grievance.referenceId}). Here's a summary:\n\n${aiSummary.response}\n\nWould you like me to generate a detailed PDF report for this grievance?`,
      timestamp: new Date(),
      metadata: {
        grievanceId: grievance._id,
        reportAvailable: true
      }
    };

    conversation.messages.push(responseMessage);
    conversation.lastMessageAt = new Date();
    await conversation.save();

    res.json({
      success: true,
      userMessage: conversation.messages[conversation.messages.length - 2],
      aiResponse: responseMessage
    });

  } catch (error) {
    logger.error('Report generation via chat failed', {
      error: error.message,
      grievanceId,
      userId
    });
    
    const errorMessage = {
      role: 'ai',
      content: 'I encountered an error while trying to generate the report. Please try again later.',
      timestamp: new Date(),
      isError: true
    };
    
    conversation.messages.push(errorMessage);
    await conversation.save();
    
    res.json({
      success: true,
      userMessage: conversation.messages[conversation.messages.length - 2],
      aiResponse: errorMessage
    });
  }
};

// Get conversation messages
const getConversationHistory = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const { limit = 50, skip = 0 } = req.query;
  const userId = req.user.userId;

  const conversation = await Chat.findById(conversationId);
  if (!conversation) {
    throw new AppError('Conversation not found', 404, 'CONVERSATION_NOT_FOUND');
  }

  // Verify ownership
  if (conversation.userId.toString() !== userId) {
    throw new AppError('Access denied', 403, 'ACCESS_DENIED');
  }

  // Get messages with pagination
  const messages = conversation.messages
    .slice(skip, skip + parseInt(limit))
    .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

  res.json({
    success: true,
    conversationId,
    messages,
    totalMessages: conversation.messages.length,
    hasMore: skip + messages.length < conversation.messages.length
  });
});

// Get user's conversations
const getUserConversations = asyncHandler(async (req, res) => {
  const userId = req.user.userId;
  const { page = 1, limit = 10 } = req.query;
  const skip = (page - 1) * limit;

  const conversations = await Chat.find({ userId })
    .sort({ lastMessageAt: -1 })
    .skip(skip)
    .limit(parseInt(limit))
    .select('_id createdAt lastMessageAt isActive messages');

  // Add summary info
  const conversationsWithSummary = conversations.map(conv => ({
    _id: conv._id,
    createdAt: conv.createdAt,
    lastMessageAt: conv.lastMessageAt,
    isActive: conv.isActive,
    messageCount: conv.messages.length,
    lastMessage: conv.messages.length > 0 
      ? conv.messages[conv.messages.length - 1].content.substring(0, 100)
      : null
  }));

  const totalConversations = await Chat.countDocuments({ userId });

  res.json({
    success: true,
    conversations: conversationsWithSummary,
    pagination: {
      currentPage: parseInt(page),
      totalPages: Math.ceil(totalConversations / limit),
      totalConversations,
      hasNext: skip + conversations.length < totalConversations,
      hasPrev: page > 1
    }
  });
});

// Delete conversation
const deleteConversation = asyncHandler(async (req, res) => {
  const { conversationId } = req.params;
  const userId = req.user.userId;

  const conversation = await Chat.findById(conversationId);
  if (!conversation) {
    throw new AppError('Conversation not found', 404, 'CONVERSATION_NOT_FOUND');
  }

  // Verify ownership
  if (conversation.userId.toString() !== userId) {
    throw new AppError('Access denied', 403, 'ACCESS_DENIED');
  }

  await Chat.findByIdAndDelete(conversationId);

  // Log activity
  await UserActivity.create({
    userId,
    activityType: 'chat_conversation_deleted',
    details: { conversationId },
    ipAddress: req.ip
  });

  logger.info('Chat conversation deleted', {
    userId,
    conversationId
  });

  res.json({
    success: true,
    message: 'Conversation deleted successfully'
  });
});

// Get Ollama service health
const getServiceHealth = asyncHandler(async (req, res) => {
  const health = await ollamaService.healthCheck();
  res.json({
    success: true,
    health
  });
});

export {
  startConversation,
  sendMessage,
  streamResponse,
  streamViaWebSocket,
  getConversationHistory,
  getUserConversations,
  deleteConversation,
  getServiceHealth
};