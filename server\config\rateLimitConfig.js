// Unified Rate Limiting Configuration
// Single source of truth for all rate limiting parameters

export const rateLimitConfig = {
  // Global rate limiting (applied to all requests)
  global: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // 1000 requests per window
    message: {
      error: 'Too many requests from this IP, please try again later.',
      code: 'GLOBAL_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health' || req.path === '/api/health';
    },
    keyGenerator: (req) => {
      // Use forwarded IP if behind proxy
      return req.ip || req.connection.remoteAddress;
    }
  },

  // Authentication rate limiting (login, register, password reset)
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 attempts per window
    message: {
      error: 'Too many authentication attempts, please try again later.',
      code: 'AUTH_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skipSuccessfulRequests: true
  },

  // API rate limiting (general API endpoints)
  api: {
    windowMs: 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
    message: {
      error: 'API rate limit exceeded, please try again later.',
      code: 'API_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip for health checks and static assets
      return req.path.startsWith('/health') || req.path.startsWith('/static');
    }
  },

  // Upload rate limiting (file uploads, chat messages)
  upload: {
    windowMs: 60 * 1000, // 1 minute
    max: 10, // 10 uploads per minute
    message: {
      error: 'Too many upload attempts, please try again later.',
      code: 'UPLOAD_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false
  },

  // Admin rate limiting (admin operations)
  admin: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    max: 100, // 100 requests per 5 minutes for admin operations
    message: {
      error: 'Admin API rate limit exceeded, please try again later.',
      code: 'ADMIN_RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false
  }
};

// Rate limit types enum for consistency
export const RATE_LIMIT_TYPES = {
  GLOBAL: 'global',
  AUTH: 'auth',
  API: 'api',
  UPLOAD: 'upload',
  ADMIN: 'admin'
};

export default rateLimitConfig;
