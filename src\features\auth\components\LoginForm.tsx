import React, { useState, useCallback, memo } from 'react';
import { motion } from 'framer-motion';
import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Checkbox } from '../../../components/ui/checkbox';
import { LoginSuccessDialog } from './LoginSuccessDialog';
import { useGradientTheme } from '../../../providers/GradientThemeProvider';
import { useAuthForm } from '../hooks/useAuthForm';
import { Eye, EyeOff, Mail, Lock, Check, X } from 'lucide-react';
import '../../../styles/tooltip.css';
import '../../../styles/autofill-fix.css';
import '../../../styles/animations.css';

interface LoginFormProps {
  onSuccess: () => void;
  onSwitchToRegister: () => void;
  onForgotPassword: () => void;
}

export const LoginForm: React.FC<LoginFormProps> = memo(({ 
  onSuccess, 
  onSwitchToRegister, 
  onForgotPassword 
}) => {
  const [showPassword, setShowPassword] = useState(false);
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const { themeConfig } = useGradientTheme();

  
  const handleSuccessWithDialog = useCallback(() => {
    setShowSuccessDialog(true);
    setTimeout(() => {
      onSuccess();
    }, 2500);
  }, [onSuccess]);
  
  const { 
    formData, 
    errors, 
    isSubmitting, 
    emailValidationStatus,
    rememberMe,
    setRememberMe,
    updateField, 
    handleLogin 
  } = useAuthForm({
    onSuccess: handleSuccessWithDialog,
  });

  const isFormValid = useCallback(() => {
    return formData.gmail && formData.password;
  }, [formData.gmail, formData.password]);

  const onSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    await handleLogin();
  }, [handleLogin]);

  const togglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  return (
    <>
      <motion.form
        onSubmit={onSubmit}
        className="space-y-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
      <div className="space-y-4">
        <motion.div>
          <Label htmlFor="gmail" className="text-base font-semibold">Gmail Address</Label>
        </motion.div>
        <div className="relative group">
          <Mail className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            id="gmail"
            type="email"
            placeholder="Enter your email"
            value={formData.gmail || ''}
            onChange={(e) => updateField('gmail', e.target.value)}
            className={`pl-12 pr-12 py-4 text-base h-input transition-all duration-200 ${
              errors.gmail ? 'border-red-500 animate-shake' : ''
            }`}
            disabled={isSubmitting}
          />
          
          {/* Email validation indicator */}
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
            {emailValidationStatus === 'checking' && (
              <div className="animate-spin h-4 w-4 border-2 border-primary border-t-transparent rounded-full" />
            )}
            {emailValidationStatus === 'available' && (
              <Check className="h-4 w-4 text-green-500" />
            )}
            {emailValidationStatus === 'taken' && (
              <X className="h-4 w-4 text-red-500" />
            )}
          </div>
          
          <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
            Enter your email address
          </div>
        </div>
        {errors.gmail && (
          <motion.div 
            className="glass-light px-3 py-2 rounded-xl border border-red-500/20 bg-red-500/5"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <p className="text-sm text-red-500 font-medium flex items-center gap-2">
              <X className="h-3 w-3" />
              {errors.gmail}
            </p>
          </motion.div>
        )}
        
        {emailValidationStatus === 'taken' && !errors.gmail && (
          <motion.div 
            className="glass-light px-3 py-2 rounded-xl border border-red-500/20 bg-red-500/5"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <p className="text-sm text-red-500 font-medium flex items-center gap-2">
              <X className="h-3 w-3" />
              This email is already registered
            </p>
          </motion.div>
        )}
      </div>

      <div className="space-y-4">
        <motion.div>
          <Label htmlFor="password" className="text-base font-semibold">Password</Label>
        </motion.div>
        <div className="relative group">
          <Lock className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            placeholder="Enter your password"
            value={formData.password || ''}
            onChange={(e) => updateField('password', e.target.value)}
            className={`pl-12 pr-14 py-4 text-base h-input transition-all duration-200 ${
              errors.password ? 'border-red-500 animate-shake' : ''
            }`}
            disabled={isSubmitting}
          />
          <div className="absolute top-full left-4 mt-2 px-3 py-1.5 glass text-foreground text-sm font-medium rounded-xl opacity-0 group-hover:group-focus-within:opacity-0 group-hover:opacity-100 transition-opacity duration-150 whitespace-nowrap pointer-events-none z-[100] shadow-theme-lg">
            Enter your secure password
          </div>
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-btn-sm w-btn-icon p-0 rounded-xl transition-all duration-instant hover:bg-primary/20 flex items-center justify-center"
                onClick={togglePassword}
              >
                <motion.div
                  animate={{ rotateY: showPassword ? 180 : 0 }}
                  transition={{ duration: 0.3 }}
                  className=""
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </motion.div>
              </Button>
          </div>
        </div>
        {errors.password && (
          <motion.div 
            className="glass-light px-3 py-2 rounded-xl border border-red-500/20 bg-red-500/5"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2 }}
          >
            <p className="text-sm text-red-500 font-medium flex items-center gap-2">
              <X className="h-3 w-3" />
              {errors.password}
            </p>
          </motion.div>
        )}
        <div className="flex justify-end -mt-1">
          <Button
            type="button"
            variant="link"
            className="p-0 h-auto text-sm font-medium"
            onClick={onForgotPassword}
          >
            Forgot password
          </Button>
        </div>
      </div>

      {/* Remember Me Checkbox */}
      <div className="flex items-center space-x-2 pt-2">
        <Checkbox 
          id="remember-me"
          checked={rememberMe}
          onCheckedChange={setRememberMe}
          className="rounded-md"
        />
        <Label 
          htmlFor="remember-me" 
          className="text-sm font-medium cursor-pointer"
        >
          Remember me
        </Label>
      </div>

      <div className="pt-2">
        <Button
          type="submit"
          className="w-full h-btn-lg text-base font-semibold transition-all duration-200 disabled:opacity-50"
          disabled={isSubmitting || !isFormValid()}
        >
          {isSubmitting ? (
            <div className="flex items-center gap-2">
              <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full" />
              <span>Authenticating...</span>
            </div>
          ) : (
            'Sign In'
          )}
        </Button>
      </div>

      <div className="text-center pt-2">
        <p className="text-base text-muted-foreground">
          Don't have an account?{' '}
          <Button
            type="button"
            variant="link"
            className="p-0 h-auto text-base font-semibold"
            onClick={onSwitchToRegister}
          >
            Sign up
          </Button>
        </p>
      </div>
      </motion.form>
      
      <LoginSuccessDialog 
        isOpen={showSuccessDialog} 
        onClose={() => setShowSuccessDialog(false)}
        theme={themeConfig}
      />
    </>
  );
});