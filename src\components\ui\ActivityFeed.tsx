import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card';
import { Avatar, AvatarFallback } from './avatar';
import { Badge } from './badge';
import { useGradientTheme } from '../../providers/GradientThemeProvider';
import {
  CheckCircledIcon,
  ClockIcon,
  ChatBubbleIcon,
  FileTextIcon,
  PersonIcon,
  UpdateIcon,
  ExclamationTriangleIcon
} from '@radix-ui/react-icons';
import { Activity, MessageSquare, FileCheck, AlertCircle, UserCheck, Settings } from 'lucide-react';

interface ActivityItem {
  id: string;
  type: 'resolved' | 'submitted' | 'message' | 'update';
  title: string;
  description: string;
  time: string;
  user?: string;
  status?: string;
}

const activityData: ActivityItem[] = [
  {
    id: '1',
    type: 'resolved',
    title: 'Grievance Resolved',
    description: 'Road repair complaint #GR-001 has been successfully resolved by municipal team',
    time: '2 hours ago',
    user: 'Admin',
    status: 'Completed'
  },
  {
    id: '2',
    type: 'submitted',
    title: 'Grievance Submitted',
    description: 'New water supply complaint #GR-004 submitted for review',
    time: '4 hours ago',
    user: 'You',
    status: 'Pending'
  },
  {
    id: '3',
    type: 'message',
    title: 'Remark Added',
    description: 'Official remark added to your street light maintenance request',
    time: '6 hours ago',
    user: 'Support Team'
  },
  {
    id: '4',
    type: 'update',
    title: 'Status Update',
    description: 'Garbage collection complaint moved from pending to under review',
    time: '1 day ago',
    user: 'System',
    status: 'Under Review'
  },
  {
    id: '5',
    type: 'resolved',
    title: 'Resolution Completed',
    description: 'Pothole repair grievance #GR-002 marked as resolved with photos',
    time: '2 days ago',
    user: 'Field Officer',
    status: 'Completed'
  },
  {
    id: '6',
    type: 'message',
    title: 'AI Chat Response',
    description: 'Received automated guidance for your drainage complaint query',
    time: '3 days ago',
    user: 'AI Assistant'
  }
];

export const ActivityFeed: React.FC = () => {
  const { themeConfig } = useGradientTheme();

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'resolved':
        return FileCheck;
      case 'submitted':
        return FileTextIcon;
      case 'message':
        return MessageSquare;
      case 'update':
        return AlertCircle;
      default:
        return Activity;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'resolved':
        return '#10b981';
      case 'submitted':
        return '#3b82f6';
      case 'message':
        return '#8b5cf6';
      case 'update':
        return '#f59e0b';
      default:
        return themeConfig.accentColor;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'Completed':
        return '#10b981';
      case 'In Progress':
        return '#f59e0b';
      case 'Under Review':
        return '#3b82f6';
      default:
        return '#6b7280';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 1.1 }}
    >
      <Card
        className="border-0 h-fit"
        style={{
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          background: 'var(--glass-bg)',
          border: '1px solid var(--glass-border)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
        }}
      >
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3 mb-2">
            <motion.div
              whileHover={{ rotate: 5, scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Activity 
                className="w-5 h-5" 
                style={{ 
                  color: themeConfig.accentColor,
                  filter: `drop-shadow(0 0 8px ${themeConfig.accentColor}60)`
                }}
              />
            </motion.div>
            <CardTitle 
              className="text-xl font-black"
              style={{ 
                color: 'var(--theme-text)',
                textShadow: `0 0 20px var(--theme-text)40, 0 0 40px var(--theme-text)20`
              }}
            >
              Recent Activity
            </CardTitle>
          </div>
          <p 
            className="text-sm font-medium opacity-80"
            style={{ 
              color: 'var(--theme-text)',
              textShadow: `0 0 15px var(--theme-text)30`
            }}
          >
            Your latest interactions
          </p>
        </CardHeader>
        <CardContent>
          <div 
            className="space-y-4 overflow-y-auto pr-2"
            style={{
              height: '75vh',
              maxHeight: '75vh',
              scrollbarWidth: 'thin',
              scrollbarColor: `${themeConfig.accentColor}40 transparent`
            }}
          >
            <style jsx>{`
              div::-webkit-scrollbar {
                width: 4px;
              }
              div::-webkit-scrollbar-track {
                background: transparent;
              }
              div::-webkit-scrollbar-thumb {
                background: ${themeConfig.accentColor}40;
                border-radius: 2px;
              }
              div::-webkit-scrollbar-thumb:hover {
                background: ${themeConfig.accentColor}60;
              }
            `}</style>
            {activityData.map((item, index) => {
              const IconComponent = getActivityIcon(item.type);
              const activityColor = getActivityColor(item.type);
              
              return (
                <motion.div
                  key={item.id}
                  className="flex gap-3 p-3 rounded-xl transition-all duration-300 cursor-pointer group"
                  style={{ backgroundColor: 'transparent' }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 1.2 + index * 0.1 }}
                  whileHover={{ scale: 1.01 }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  {/* Activity Icon */}
                  <motion.div
                    className="flex-shrink-0 w-11 h-11 rounded-full flex items-center justify-center"
                    whileHover={{ 
                      rotate: 8, 
                      scale: 1.1
                    }}
                    transition={{ duration: 0.2, type: "spring", stiffness: 300 }}
                  >
                    <IconComponent 
                      className="w-5 h-5"
                      style={{ 
                        color: activityColor,
                        filter: `drop-shadow(0 0 8px ${activityColor}60)`
                      }}
                    />
                  </motion.div>

                  {/* Activity Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2 mb-1">
                      <h4 
                        className="font-bold text-sm"
                        style={{ 
                          color: 'var(--theme-text)',
                          textShadow: `0 0 15px var(--theme-text)30`
                        }}
                      >
                        {item.title}
                      </h4>
                      {item.status && (
                        <Badge 
                          className="text-xs px-2 py-1 rounded-full font-medium flex-shrink-0"
                          style={{ 
                            backgroundColor: `${getStatusColor(item.status)}20`,
                            color: getStatusColor(item.status),
                            border: `1px solid ${getStatusColor(item.status)}40`
                          }}
                        >
                          {item.status}
                        </Badge>
                      )}
                    </div>
                    
                    <p 
                      className="text-xs opacity-80 mb-2 line-clamp-2 font-medium"
                      style={{ 
                        color: 'var(--theme-text)',
                        textShadow: `0 0 10px var(--theme-text)20`
                      }}
                    >
                      {item.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-5 w-5">
                          <AvatarFallback 
                            className="text-xs font-medium"
                            style={{ 
                              backgroundColor: `${activityColor}30`,
                              color: activityColor
                            }}
                          >
                            {item.user?.charAt(0) || 'S'}
                          </AvatarFallback>
                        </Avatar>
                        <span 
                          className="text-xs opacity-60"
                          style={{ color: 'var(--theme-text)' }}
                        >
                          {item.user}
                        </span>
                      </div>
                      
                      <span 
                        className="text-xs opacity-50"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {item.time}
                      </span>
                    </div>
                  </div>

                  {/* Hover Indicator */}
                  <motion.div
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0"
                    initial={{ x: -10 }}
                    whileHover={{ x: 0 }}
                  >
                    <div 
                      className="w-1 h-8 rounded-full"
                      style={{ backgroundColor: activityColor }}
                    />
                  </motion.div>
                </motion.div>
              );
            })}

          </div>
          
          {/* Load More Button */}
          <motion.div
            className="mt-6 pt-4"
            style={{ borderTop: '1px solid var(--glass-border)' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
          >
            <motion.button
              className="w-full py-2 px-4 rounded-xl text-sm font-medium transition-all duration-300"
              style={{
                backgroundColor: 'transparent',
                color: 'var(--theme-text)',
                border: '1px solid var(--glass-border)'
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                e.currentTarget.style.borderColor = themeConfig.accentColor + '40';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.borderColor = 'var(--glass-border)';
              }}
            >
              Load More Activities
            </motion.button>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
};