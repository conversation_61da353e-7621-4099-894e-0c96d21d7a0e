/* Authentication Form Animations */

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-4px); }
  20%, 40%, 60%, 80% { transform: translateX(4px); }
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes pulse-error {
  0% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4); }
  70% { box-shadow: 0 0 0 6px rgba(239, 68, 68, 0); }
  100% { box-shadow: 0 0 0 0 rgba(239, 68, 68, 0); }
}

.animate-pulse-error {
  animation: pulse-error 1s ease-out;
}

@keyframes bounce-in {
  0% { 
    transform: scale(0.3) translateY(-50px);
    opacity: 0;
  }
  50% { 
    transform: scale(1.05) translateY(-10px);
    opacity: 0.8;
  }
  70% { 
    transform: scale(0.9) translateY(0);
    opacity: 0.9;
  }
  100% { 
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.animate-bounce-in {
  animation: bounce-in 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slide-up {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

.animate-slide-up {
  animation: slide-up 0.3s ease-out;
}

/* Success Dialog Animations */
@keyframes success-scale {
  0% { 
    transform: scale(0) rotate(-180deg);
    opacity: 0;
  }
  50% { 
    transform: scale(1.2) rotate(-90deg);
    opacity: 0.8;
  }
  100% { 
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

.animate-success-scale {
  animation: success-scale 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Loading Spinner */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin-slow 2s linear infinite;
}

/* Theme-aware glow effects */
@keyframes glow-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
  }
  50% { 
    box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}