// Ollama LLM Service Integration
import config from '../config/environment.js';
import { logger } from '../middleware/logging.middleware.js';

class OllamaService {
  constructor() {
    this.baseURL = config.OLLAMA_BASE_URL || 'http://localhost:11434';
    this.defaultModel = 'llama2'; // Default model, can be configured
    this.maxTokens = 2000;
    this.temperature = 0.7;
  }

  // Check if Ollama service is available
  async isAvailable() {
    try {
      const response = await fetch(`${this.baseURL}/api/tags`, {
        method: 'GET',
        timeout: 5000
      });
      return response.ok;
    } catch (error) {
      logger.error('Ollama service unavailable', { error: error.message });
      return false;
    }
  }

  // Get available models
  async getAvailableModels() {
    try {
      const response = await fetch(`${this.baseURL}/api/tags`);
      if (!response.ok) {
        throw new Error('Failed to fetch models');
      }
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      logger.error('Failed to get available models', { error: error.message });
      return [];
    }
  }

  // Generate AI response with context
  async generateResponse(prompt, context = [], options = {}) {
    try {
      const {
        model = this.defaultModel,
        temperature = this.temperature,
        maxTokens = this.maxTokens,
        stream = false
      } = options;

      // Build context-aware prompt
      const contextualPrompt = this.buildContextualPrompt(prompt, context);

      const requestBody = {
        model,
        prompt: contextualPrompt,
        stream,
        options: {
          temperature,
          num_predict: maxTokens,
          top_p: 0.9,
          top_k: 40
        }
      };

      logger.info('Generating Ollama response', {
        model,
        promptLength: prompt.length,
        contextLength: context.length,
        stream
      });

      const response = await fetch(`${this.baseURL}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`Ollama API error: ${response.status} ${response.statusText}`);
      }

      if (stream) {
        return response; // Return response for streaming
      } else {
        const data = await response.json();
        return {
          response: data.response,
          model: data.model,
          done: data.done,
          context: data.context,
          totalDuration: data.total_duration,
          loadDuration: data.load_duration,
          promptEvalCount: data.prompt_eval_count,
          evalCount: data.eval_count
        };
      }
    } catch (error) {
      logger.error('Ollama generation failed', {
        error: error.message,
        prompt: prompt.substring(0, 100)
      });
      throw error;
    }
  }

  // Stream AI response with WebSocket integration
  async streamResponseToSocket(socket, conversationId, prompt, context = [], options = {}) {
    const startTime = Date.now();
    let fullResponse = '';
    let tokenCount = 0;
    
    try {
      // Emit streaming start event
      socket.emit('chat:stream_start', {
        conversationId,
        timestamp: new Date().toISOString()
      });

      const response = await this.generateResponse(prompt, context, {
        ...options,
        stream: true
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop();

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              const chunk = data.response || '';
              
              if (chunk) {
                fullResponse += chunk;
                tokenCount++;
                
                // Emit chunk to client
                socket.emit('chat:stream_chunk', {
                  conversationId,
                  chunk,
                  timestamp: new Date().toISOString()
                });
              }

              if (data.done) {
                const duration = Date.now() - startTime;
                
                // Emit completion event
                socket.emit('chat:stream_complete', {
                  conversationId,
                  fullResponse,
                  duration,
                  tokenCount,
                  model: data.model,
                  timestamp: new Date().toISOString()
                });
                
                logger.info('Chat streaming completed', {
                  conversationId,
                  duration,
                  tokenCount,
                  responseLength: fullResponse.length
                });
                
                return {
                  response: fullResponse,
                  duration,
                  tokenCount,
                  model: data.model
                };
              }
            } catch (parseError) {
              logger.warn('Failed to parse streaming response', {
                line: line.substring(0, 100),
                error: parseError.message
              });
            }
          }
        }
      }
    } catch (error) {
      logger.error('Chat streaming failed', {
        conversationId,
        error: error.message,
        duration: Date.now() - startTime
      });
      
      // Emit error to client
      socket.emit('chat:stream_error', {
        conversationId,
        error: 'Streaming failed. Please try again.',
        timestamp: new Date().toISOString()
      });
      
      throw error;
    }
  }

  // Stream AI response (generator version for non-WebSocket use)
  async *streamResponse(prompt, context = [], options = {}) {
    try {
      const response = await this.generateResponse(prompt, context, {
        ...options,
        stream: true
      });

      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const { done, value } = await reader.read();
        
        if (done) break;

        buffer += decoder.decode(value, { stream: true });
        const lines = buffer.split('\n');
        buffer = lines.pop();

        for (const line of lines) {
          if (line.trim()) {
            try {
              const data = JSON.parse(line);
              yield {
                type: 'chunk',
                content: data.response || '',
                done: data.done || false,
                model: data.model,
                context: data.context
              };

              if (data.done) {
                yield {
                  type: 'complete',
                  content: '',
                  done: true,
                  totalDuration: data.total_duration,
                  loadDuration: data.load_duration,
                  promptEvalCount: data.prompt_eval_count,
                  evalCount: data.eval_count
                };
                return;
              }
            } catch (parseError) {
              logger.warn('Failed to parse streaming response', {
                line: line.substring(0, 100),
                error: parseError.message
              });
            }
          }
        }
      }
    } catch (error) {
      logger.error('Streaming failed', { error: error.message });
      yield {
        type: 'error',
        error: error.message
      };
    }
  }

  // Build contextual prompt with conversation history
  buildContextualPrompt(currentPrompt, context = []) {
    let contextualPrompt = `You are CivicAssist AI, a helpful assistant for a civic grievance management system. You help users with their grievances, provide information, and can generate reports.

Guidelines:
- Be helpful, professional, and empathetic
- Provide accurate information about grievance processes
- When asked to generate reports, provide structured summaries
- If asked about specific grievance IDs, acknowledge the request and provide relevant information
- Keep responses concise but informative

`;

    // Add conversation context
    if (context.length > 0) {
      contextualPrompt += "Previous conversation:\n";
      context.slice(-10).forEach(msg => { // Last 10 messages for context
        const role = msg.role === 'user' ? 'User' : 'Assistant';
        contextualPrompt += `${role}: ${msg.content}\n`;
      });
      contextualPrompt += "\n";
    }

    contextualPrompt += `Current question: ${currentPrompt}\n\nResponse:`;

    return contextualPrompt;
  }

  // Analyze user intent for special commands
  analyzeIntent(prompt) {
    const lowerPrompt = prompt.toLowerCase();
    
    // Report generation commands
    if (lowerPrompt.includes('generate report') || lowerPrompt.includes('create report')) {
      const grievanceIdMatch = prompt.match(/(?:grievance|id|#)\s*([a-zA-Z0-9]+)/i);
      return {
        type: 'report_generation',
        grievanceId: grievanceIdMatch ? grievanceIdMatch[1] : null,
        reportType: 'grievance'
      };
    }

    // Summary commands
    if (lowerPrompt.includes('summarize') || lowerPrompt.includes('summary')) {
      return {
        type: 'summary',
        scope: lowerPrompt.includes('my') ? 'user' : 'general'
      };
    }

    // Status check commands
    if (lowerPrompt.includes('status') && (lowerPrompt.includes('grievance') || lowerPrompt.includes('complaint'))) {
      const grievanceIdMatch = prompt.match(/(?:grievance|id|#)\s*([a-zA-Z0-9]+)/i);
      return {
        type: 'status_check',
        grievanceId: grievanceIdMatch ? grievanceIdMatch[1] : null
      };
    }

    // Help commands
    if (lowerPrompt.includes('help') || lowerPrompt.includes('how to')) {
      return {
        type: 'help',
        topic: 'general'
      };
    }

    return {
      type: 'general_query'
    };
  }

  // Generate system prompt for specific contexts
  getSystemPrompt(context = 'general') {
    const prompts = {
      general: "You are CivicAssist AI, a helpful assistant for civic grievance management.",
      report: "You are generating a report summary. Be factual, structured, and comprehensive.",
      help: "You are providing help and guidance. Be clear, step-by-step, and supportive.",
      status: "You are providing status information. Be accurate and informative."
    };

    return prompts[context] || prompts.general;
  }

  // Validate and sanitize user input
  validateInput(prompt) {
    if (!prompt || typeof prompt !== 'string') {
      throw new Error('Invalid prompt: must be a non-empty string');
    }

    if (prompt.length > 4000) {
      throw new Error('Prompt too long: maximum 4000 characters allowed');
    }

    // Remove potentially harmful content
    const sanitized = prompt
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();

    return sanitized;
  }

  // Note: Rate limiting is now handled by unified middleware
  // Custom streaming rate limits can be implemented via route-level middleware if needed

  // Get model information
  async getModelInfo(modelName = this.defaultModel) {
    try {
      const response = await fetch(`${this.baseURL}/api/show`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ name: modelName })
      });

      if (!response.ok) {
        throw new Error('Failed to get model info');
      }

      return await response.json();
    } catch (error) {
      logger.error('Failed to get model info', {
        model: modelName,
        error: error.message
      });
      return null;
    }
  }

  // Health check for the service
  async healthCheck() {
    try {
      const isAvailable = await this.isAvailable();
      const models = await this.getAvailableModels();
      
      return {
        status: isAvailable ? 'healthy' : 'unhealthy',
        baseURL: this.baseURL,
        modelsAvailable: models.length,
        defaultModel: this.defaultModel,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Export singleton instance
const ollamaService = new OllamaService();

export {
  ollamaService,
  OllamaService
};

export default ollamaService;