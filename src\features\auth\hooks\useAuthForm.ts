import { useState, useCallback, useEffect } from "react";
import { useAuthStore } from "../../../store/useAuthStore";
import { authService } from "../services/authService";
import { toast } from "../../../hooks/use-toast";
import { toastUtils } from "../../../utils/toastUtils";
import { rateLimitUtils } from "../../../utils/rateLimitUtils";

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

interface UseAuthFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const useAuthForm = ({ onSuccess, onError }: UseAuthFormProps = {}) => {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailValidationStatus, setEmailValidationStatus] = useState<
    "idle" | "checking" | "available" | "taken"
  >("idle");
  const [rememberMe, setRememberMe] = useState(false);
  const [lastSubmitTime, setLastSubmitTime] = useState(0);

  const { login, register, setError } = useAuthStore();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (
    password: string
  ): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (password.length < 8) errors.push("At least 8 characters");
    if (!/[A-Z]/.test(password)) errors.push("One uppercase letter");
    if (!/[a-z]/.test(password)) errors.push("One lowercase letter");
    if (!/\d/.test(password)) errors.push("One number");
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password))
      errors.push("One special character");

    return { isValid: errors.length === 0, errors };
  };

  const checkGmailExists = useCallback(
    async (gmail: string): Promise<boolean> => {
      if (!validateEmail(gmail)) return false;

      setEmailValidationStatus("checking");
      try {
        const exists = await authService.checkGmailExists(gmail);
        setEmailValidationStatus(exists ? "taken" : "available");
        return exists;
      } catch {
        setEmailValidationStatus("idle");
        return false;
      }
    },
    []
  );

  // Debounced email checking
  const debouncedEmailCheck = useCallback(
    debounce(async (email: string) => {
      if (validateEmail(email)) {
        await checkGmailExists(email);
      }
    }, 500),
    [checkGmailExists]
  );

  const updateField = useCallback(
    (field: string, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));

      // Clear errors when user starts typing
      if (errors[field]) {
        setErrors((prev) => ({ ...prev, [field]: "" }));
      }

      // Real-time email validation
      if (field === "gmail") {
        if (!value) {
          setEmailValidationStatus("idle");
        } else if (!validateEmail(value)) {
          setErrors((prev) => ({ ...prev, gmail: "Invalid email format" }));
          setEmailValidationStatus("idle");
        } else {
          setErrors((prev) => ({ ...prev, gmail: "" }));
          // Trigger debounced email existence check
          debouncedEmailCheck(value);
        }
      }

      // Real-time password confirmation
      if (field === "confirmPassword" && formData.password) {
        if (value !== formData.password) {
          setErrors((prev) => ({
            ...prev,
            confirmPassword: "Passwords do not match",
          }));
        } else {
          setErrors((prev) => ({ ...prev, confirmPassword: "" }));
        }
      }
    },
    [errors, formData.password, debouncedEmailCheck]
  );

  const validateForm = useCallback(
    (type: "login" | "register"): boolean => {
      const newErrors: Record<string, string> = {};

      // Email validation
      if (!formData.gmail?.trim()) {
        newErrors.gmail = "Email is required";
      } else if (!validateEmail(formData.gmail)) {
        newErrors.gmail = "Invalid email format";
      }

      // Password validation
      if (!formData.password?.trim()) {
        newErrors.password = "Password is required";
      } else if (type === "register") {
        const passwordValidation = validatePassword(formData.password);
        if (!passwordValidation.isValid) {
          newErrors.password = passwordValidation.errors.join(", ");
        }
      }

      // Registration-specific validations
      if (type === "register") {
        if (!formData.fullName?.trim()) {
          newErrors.fullName = "Full name is required";
        }

        if (!formData.confirmPassword?.trim()) {
          newErrors.confirmPassword = "Please confirm your password";
        } else if (formData.confirmPassword !== formData.password) {
          newErrors.confirmPassword = "Passwords do not match";
        }
      }

      setErrors(newErrors);

      // Shake animation for invalid fields
      if (Object.keys(newErrors).length > 0) {
        Object.keys(newErrors).forEach((field) => {
          const element = document.getElementById(field);
          if (element) {
            element.classList.add("animate-shake");
            setTimeout(() => element.classList.remove("animate-shake"), 500);
          }
        });
      }

      return Object.keys(newErrors).length === 0;
    },
    [formData]
  );

  const handleLogin = useCallback(async () => {
    if (!validateForm("login")) return false;

    // Prevent multiple rapid submissions (debounce)
    const now = Date.now();
    if (now - lastSubmitTime < 500) {
      toastUtils.auth.rateLimiting.pleaseWait();
      return false;
    }
    setLastSubmitTime(now);

    setIsSubmitting(true);

    // Artificial delays removed - let server handle timing naturally

    try {
      const success = await login(
        formData.gmail,
        formData.password,
        rememberMe
      );

      if (success) {
        toastUtils.auth.loginSuccess();
        setTimeout(() => onSuccess?.(), 1500);
        return true;
      } else {
        return false;
      }
    } catch (error: any) {
      let errorMessage = error.message || "Login failed";

      // Process error message for rate limiting
      errorMessage = rateLimitUtils.processErrorMessage(errorMessage);

      toastUtils.auth.loginFailed(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, login, onSuccess, validateForm, rememberMe, lastSubmitTime]);

  const handleRegister = useCallback(async () => {
    if (!validateForm("register")) return false;

    // Check if email is already taken
    if (emailValidationStatus === "taken") {
      setErrors((prev) => ({
        ...prev,
        gmail: "This email is already registered",
      }));
      return false;
    }

    // Prevent multiple rapid submissions (debounce)
    const now = Date.now();
    if (now - lastSubmitTime < 500) {
      toastUtils.auth.rateLimiting.pleaseWait();
      return false;
    }
    setLastSubmitTime(now);

    setIsSubmitting(true);

    // Artificial delays removed - let server handle timing naturally

    try {
      const success = await register(
        formData.gmail,
        formData.password,
        formData.fullName,
        formData.mobile,
        formData.address
      );

      if (success) {
        toastUtils.auth.registrationSuccess();
        onSuccess?.();
        return true;
      } else {
        return false;
      }
    } catch (error: any) {
      let errorMessage = error.message || "Registration failed";

      // Process error message for rate limiting
      errorMessage = rateLimitUtils.processErrorMessage(errorMessage);

      toastUtils.auth.registrationFailed(errorMessage);
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [
    formData,
    register,
    onSuccess,
    validateForm,
    emailValidationStatus,
    lastSubmitTime,
  ]);

  return {
    formData,
    errors,
    isSubmitting,
    emailValidationStatus,
    rememberMe,
    setRememberMe,
    updateField,
    handleLogin,
    handleRegister,
    validatePassword,
    checkGmailExists,
  };
};
