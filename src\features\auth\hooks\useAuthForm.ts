import { useState, useCallback, useEffect } from 'react';
import { useAuthStore } from '../../../store/useAuthStore';
import { authService } from '../services/authService';
import { toast } from '../../../hooks/use-toast';

interface UseAuthFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const useAuthForm = ({ onSuccess, onError }: UseAuthFormProps = {}) => {
  const [formData, setFormData] = useState<Record<string, string>>({});
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailValidationStatus, setEmailValidationStatus] = useState<'idle' | 'checking' | 'available' | 'taken'>('idle');
  const [rememberMe, setRememberMe] = useState(false);
  
  const { login, register, setError } = useAuthStore();

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];
    
    if (password.length < 8) errors.push('At least 8 characters');
    if (!/[A-Z]/.test(password)) errors.push('One uppercase letter');
    if (!/[a-z]/.test(password)) errors.push('One lowercase letter');
    if (!/\d/.test(password)) errors.push('One number');
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) errors.push('One special character');
    
    return { isValid: errors.length === 0, errors };
  };

  const checkGmailExists = useCallback(async (gmail: string): Promise<boolean> => {
    if (!validateEmail(gmail)) return false;
    
    setEmailValidationStatus('checking');
    try {
      const exists = await authService.checkGmailExists(gmail);
      setEmailValidationStatus(exists ? 'taken' : 'available');
      return exists;
    } catch {
      setEmailValidationStatus('idle');
      return false;
    }
  }, []);

  const updateField = useCallback((field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
    
    // Real-time email validation
    if (field === 'gmail') {
      if (!value) {
        setEmailValidationStatus('idle');
      } else if (!validateEmail(value)) {
        setErrors(prev => ({ ...prev, gmail: 'Invalid email format' }));
        setEmailValidationStatus('idle');
      } else {
        setErrors(prev => ({ ...prev, gmail: '' }));
        setEmailValidationStatus('available'); // Always show as available for now
      }
    }
    
    // Real-time password confirmation
    if (field === 'confirmPassword' && formData.password) {
      if (value !== formData.password) {
        setErrors(prev => ({ ...prev, confirmPassword: 'Passwords do not match' }));
      } else {
        setErrors(prev => ({ ...prev, confirmPassword: '' }));
      }
    }
  }, [errors, formData.password, checkGmailExists]);

  const validateForm = useCallback((type: 'login' | 'register'): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Email validation
    if (!formData.gmail?.trim()) {
      newErrors.gmail = 'Email is required';
    } else if (!validateEmail(formData.gmail)) {
      newErrors.gmail = 'Invalid email format';
    }
    
    // Password validation
    if (!formData.password?.trim()) {
      newErrors.password = 'Password is required';
    } else if (type === 'register') {
      const passwordValidation = validatePassword(formData.password);
      if (!passwordValidation.isValid) {
        newErrors.password = passwordValidation.errors.join(', ');
      }
    }
    
    // Registration-specific validations
    if (type === 'register') {
      if (!formData.fullName?.trim()) {
        newErrors.fullName = 'Full name is required';
      }
      
      if (!formData.confirmPassword?.trim()) {
        newErrors.confirmPassword = 'Please confirm your password';
      } else if (formData.confirmPassword !== formData.password) {
        newErrors.confirmPassword = 'Passwords do not match';
      }
    }
    
    setErrors(newErrors);
    
    // Shake animation for invalid fields
    if (Object.keys(newErrors).length > 0) {
      Object.keys(newErrors).forEach(field => {
        const element = document.getElementById(field);
        if (element) {
          element.classList.add('animate-shake');
          setTimeout(() => element.classList.remove('animate-shake'), 500);
        }
      });
    }
    
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleLogin = useCallback(async () => {
    if (!validateForm('login')) return false;
    
    setIsSubmitting(true);
    
    // Check if rate limiting is disabled
    const rateLimitDisabled = localStorage.getItem('rateLimitDisabled') === 'true';
    
    // Add increased delay for better UX (1000ms for login)
    if (!rateLimitDisabled) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    try {
      const success = await login(formData.gmail, formData.password, rememberMe);
      
      if (success) {
        toast({
          title: "Login Successful! 🎉",
          description: "Redirecting to dashboard...",
          duration: 2000,
        });
        setTimeout(() => onSuccess?.(), 1500);
        return true;
      } else {
        return false;
      }
    } catch (error: any) {
      const rateLimitDisabled = localStorage.getItem('rateLimitDisabled') === 'true';
      let errorMessage = error.message || 'Login failed';
      let toastTitle = "Login Failed";
      
      // Handle specific error cases with appropriate icons
      if (errorMessage.includes('No account found')) {
        toastTitle = "Account Not Found 🔍";
      } else if (errorMessage.includes('Invalid email or password')) {
        toastTitle = "Invalid Credentials ❌";
      } else if (errorMessage.includes('locked')) {
        toastTitle = "Account Locked 🔒";
      } else if (errorMessage.includes('Network') || errorMessage.includes('timeout')) {
        toastTitle = "Connection Error 🌐";
      } else if (errorMessage.includes('server error')) {
        toastTitle = "Server Error ⚠️";
      }
      
      // Suppress rate limit warnings if bypass is enabled
      if (rateLimitDisabled && (errorMessage.includes('rate limit') || errorMessage.includes('too many'))) {
        errorMessage = 'Login failed - please check credentials';
        toastTitle = "Login Failed";
      }
      
      toast({
        title: toastTitle,
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, login, onSuccess, validateForm, rememberMe]);

  const handleRegister = useCallback(async () => {
    if (!validateForm('register')) return false;
    
    // Check if email is already taken
    if (emailValidationStatus === 'taken') {
      setErrors(prev => ({ ...prev, gmail: 'This email is already registered' }));
      return false;
    }
    
    setIsSubmitting(true);
    
    // Check if rate limiting is disabled
    const rateLimitDisabled = localStorage.getItem('rateLimitDisabled') === 'true';
    
    // Add increased delay for better UX (1200ms for registration)
    if (!rateLimitDisabled) {
      await new Promise(resolve => setTimeout(resolve, 1200));
    }
    
    try {
      const success = await register(
        formData.gmail, 
        formData.password, 
        formData.fullName,
        formData.mobile,
        formData.address
      );
      
      if (success) {
        toast({
          title: "Registration Successful! 🎉",
          description: "Please log in with your new account.",
          duration: 3000,
        });
        onSuccess?.();
        return true;
      } else {
        return false;
      }
    } catch (error: any) {
      const rateLimitDisabled = localStorage.getItem('rateLimitDisabled') === 'true';
      let errorMessage = error.message || 'Registration failed';
      let toastTitle = "Registration Failed";
      
      // Handle specific error cases with appropriate icons
      if (errorMessage.includes('already registered')) {
        toastTitle = "Email Already Exists 📧";
      } else if (errorMessage.includes('Network') || errorMessage.includes('timeout')) {
        toastTitle = "Connection Error 🌐";
      } else if (errorMessage.includes('server error')) {
        toastTitle = "Server Error ⚠️";
      }
      
      // Suppress rate limit warnings if bypass is enabled
      if (rateLimitDisabled && (errorMessage.includes('rate limit') || errorMessage.includes('too many'))) {
        errorMessage = 'Registration failed - please check form data';
        toastTitle = "Registration Failed";
      }
      
      toast({
        title: toastTitle,
        description: errorMessage,
        variant: "destructive",
      });
      return false;
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, register, onSuccess, validateForm, emailValidationStatus]);

  return {
    formData,
    errors,
    isSubmitting,
    emailValidationStatus,
    rememberMe,
    setRememberMe,
    updateField,
    handleLogin,
    handleRegister,
    validatePassword,
    checkGmailExists,
  };
};